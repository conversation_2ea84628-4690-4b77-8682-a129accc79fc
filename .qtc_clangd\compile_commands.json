[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\freedom\\qtsource\\JcSoft\\main.cpp"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\freedom\\qtsource\\JcSoft\\mainwindow.cpp"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\freedom\\qtsource\\JcSoft\\mappingwindow.cpp"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/mappingwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\freedom\\qtsource\\JcSoft\\modbusserial.cpp"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/modbusserial.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\freedom\\qtsource\\JcSoft\\modbusserialbase.cpp"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/modbusserialbase.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\freedom\\qtsource\\JcSoft\\parseitemdialog.cpp"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/parseitemdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\freedom\\qtsource\\JcSoft\\setvaluedialog.cpp"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/setvaluedialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\freedom\\qtsource\\JcSoft\\valuefileselectiondialog.cpp"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/valuefileselectiondialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\freedom\\qtsource\\JcSoft\\dataconversionrulesdialog.cpp"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/dataconversionrulesdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\freedom\\qtsource\\JcSoft\\datamappingrulesdialog.cpp"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/datamappingrulesdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\freedom\\qtsource\\JcSoft\\crcutils.cpp"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/crcutils.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\mainwindow.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\mappingwindow.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/mappingwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\modbusserial.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/modbusserial.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\modbusserialbase.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/modbusserialbase.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\modbustestcase.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/modbustestcase.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\parseitemdialog.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/parseitemdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\setvaluedialog.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/setvaluedialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\valuefileselectiondialog.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/valuefileselectiondialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\dataconversionrulesdialog.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/dataconversionrulesdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\datamappingrulesdialog.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/datamappingrulesdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\crcutils.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/crcutils.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\ui_mainwindow.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/ui_mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\ui_mappingwindow.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/ui_mappingwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_AXCONTAINER_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft", "-ID:\\Qt\\6.8.2\\mingw_64\\include", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtPrintSupport", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtAxContainer", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.8.2\\mingw_64\\include\\QtCore", "-ID:\\freedom\\qtsource\\JcSoft\\release", "-ID:\\Qt\\6.8.2\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\Qt Creator 16.0.0-rc1\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\freedom\\qtsource\\JcSoft\\ui_setvaluedialog.h"], "directory": "D:/freedom/qtsource/JcSoft/.qtc_clangd", "file": "D:/freedom/qtsource/JcSoft/ui_setvaluedialog.h"}]