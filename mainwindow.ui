<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1382</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>JcSoft - 双串口通信</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <layout class="QHBoxLayout" name="horizontalLayoutTop">
      <item>
       <widget class="QGroupBox" name="groupBoxSerialA">
        <property name="title">
         <string>串口A配置</string>
        </property>
        <layout class="QGridLayout" name="gridLayoutA">
         <item row="0" column="0">
          <widget class="QLabel" name="labelPortA">
           <property name="text">
            <string>串口号：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QComboBox" name="comboBoxPortA"/>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="labelBaudA">
           <property name="text">
            <string>波特率：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QComboBox" name="comboBoxBaudA"/>
         </item>
         <item row="0" column="4">
          <widget class="QLabel" name="labelParityA">
           <property name="text">
            <string>校验位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="5">
          <widget class="QComboBox" name="comboBoxParityA"/>
         </item>
         <item row="0" column="6">
          <widget class="QLabel" name="labelDataA">
           <property name="text">
            <string>数据位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="7">
          <widget class="QComboBox" name="comboBoxDataA"/>
         </item>
         <item row="0" column="8">
          <widget class="QLabel" name="labelStopA">
           <property name="text">
            <string>停止位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="9">
          <widget class="QComboBox" name="comboBoxStopA"/>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBoxSerialB">
        <property name="title">
         <string>串口B配置</string>
        </property>
        <layout class="QGridLayout" name="gridLayoutB">
         <item row="0" column="0">
          <widget class="QLabel" name="labelPortB">
           <property name="text">
            <string>串口号：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QComboBox" name="comboBoxPortB"/>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="labelBaudB">
           <property name="text">
            <string>波特率：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QComboBox" name="comboBoxBaudB"/>
         </item>
         <item row="0" column="4">
          <widget class="QLabel" name="labelParityB">
           <property name="text">
            <string>校验位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="5">
          <widget class="QComboBox" name="comboBoxParityB"/>
         </item>
         <item row="0" column="6">
          <widget class="QLabel" name="labelDataB">
           <property name="text">
            <string>数据位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="7">
          <widget class="QComboBox" name="comboBoxDataB"/>
         </item>
         <item row="0" column="8">
          <widget class="QLabel" name="labelStopB">
           <property name="text">
            <string>停止位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="9">
          <widget class="QComboBox" name="comboBoxStopB"/>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <!-- UART测试列表已删除 -->
    <!-- Modbus测试列表已删除 -->
    <item>
     <layout class="QHBoxLayout" name="horizontalLayoutBottom">
      <item>
       <layout class="QVBoxLayout" name="verticalLayoutA">
        <item>
         <widget class="QGroupBox" name="groupBoxSendA">
          <property name="title">
           <string>串口A发送数据</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayoutSendA">
           <item>
            <widget class="QLineEdit" name="lineEditSendDataA">
             <property name="placeholderText">
              <string>请输入十六进制数据，例如：01 03 00 00 00 01</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButtonSendA">
             <property name="text">
              <string>发送</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxDisplayA">
          <property name="title">
           <string>串口A数据显示</string>
          </property>
          <property name="styleSheet">
           <string>QGroupBox {
    font-weight: bold;
    border: 2px solid #2196F3;
    border-radius: 8px;
    margin-top: 1ex;
    padding-top: 10px;
    background-color: #f8f9fa;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px 0 8px;
    color: #2196F3;
}</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayoutDisplayA">
           <item>
            <widget class="QTextBrowser" name="textBrowserA">
             <property name="styleSheet">
              <string>QTextBrowser {
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    background-color: #fafafa;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 10pt;
    padding: 8px;
}
QTextBrowser:focus {
    border-color: #2196F3;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayoutB">
        <item>
         <widget class="QGroupBox" name="groupBoxSendB">
          <property name="title">
           <string>串口B发送数据</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayoutSendB">
           <item>
            <widget class="QLineEdit" name="lineEditSendDataB">
             <property name="placeholderText">
              <string>请输入十六进制数据，例如：01 03 00 00 00 01</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButtonSendB">
             <property name="text">
              <string>发送</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxDisplayB">
          <property name="title">
           <string>串口B数据显示</string>
          </property>
          <property name="styleSheet">
           <string>QGroupBox {
    font-weight: bold;
    border: 2px solid #4CAF50;
    border-radius: 8px;
    margin-top: 1ex;
    padding-top: 10px;
    background-color: #f8f9fa;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px 0 8px;
    color: #4CAF50;
}</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayoutDisplayB">
           <item>
            <widget class="QTextBrowser" name="textBrowserB">
             <property name="styleSheet">
              <string>QTextBrowser {
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    background-color: #fafafa;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 10pt;
    padding: 8px;
}
QTextBrowser:focus {
    border-color: #2196F3;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QToolBar" name="mainToolBar">
   <property name="windowTitle">
    <string>工具栏</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionConnectA"/>
   <addaction name="actionConnectB"/>
   <addaction name="actionDisconnectA"/>
   <addaction name="actionDisconnectB"/>
   <addaction name="actionRefresh"/>
   <addaction name="actionClearSerial"/>
   <addaction name="separator"/>
   <addaction name="actionMapping"/>
   <addaction name="actionSetValue"/>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1382</width>
     <height>20</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>文件</string>
    </property>
    <addaction name="actionImportConfig"/>
    <addaction name="actionExportConfig"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuTest">
    <property name="title">
     <string>检测</string>
    </property>
    <addaction name="actionStartTest"/>
    <addaction name="actionStopTest"/>
    <addaction name="separator"/>
   </widget>
   <widget class="QMenu" name="menuConfig">
    <property name="title">
     <string>配置</string>
    </property>
    <addaction name="actionMapping"/>
    <addaction name="separator"/>
    <addaction name="actionSetValue"/>
   </widget>
   <widget class="QMenu" name="menuReport">
    <property name="title">
     <string>报表</string>
    </property>
    <addaction name="actionGenerateReport"/>
    <addaction name="actionViewReport"/>
   </widget>
   <widget class="QMenu" name="menuTools">
    <property name="title">
     <string>工具</string>
    </property>
    <addaction name="actionRefresh"/>
    <addaction name="separator"/>
    <addaction name="actionConnectA"/>
    <addaction name="actionConnectB"/>
    <addaction name="actionDisconnectA"/>
    <addaction name="actionDisconnectB"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuConfig"/>
   <addaction name="menuTest"/>
   <addaction name="menuReport"/>
   <addaction name="menuTools"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionExit">
   <property name="text">
    <string>退出</string>
   </property>
  </action>
  <action name="actionStartTest">
   <property name="text">
    <string>开始测试</string>
   </property>
  </action>
  <action name="actionStopTest">
   <property name="text">
    <string>停止测试</string>
   </property>
  </action>

  <action name="actionMapping">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/test.svg</normaloff>
     <normalon>:/icons/test.svg</normalon>:/icons/test.svg</iconset>
   </property>
   <property name="text">
    <string>TEST配置</string>
   </property>
   <property name="toolTip">
    <string>TEST配置</string>
   </property>
  </action>
  <action name="actionConnectA">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/connect.svg</normaloff>
     <normalon>:/icons/connect.svg</normalon>:/icons/connect.svg</iconset>
   </property>
   <property name="text">
    <string>连接串口A</string>
   </property>
   <property name="toolTip">
    <string>连接串口A</string>
   </property>
  </action>
  <action name="actionConnectB">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/connect.svg</normaloff>
     <normalon>:/icons/connect.svg</normalon>:/icons/connect.svg</iconset>
   </property>
   <property name="text">
    <string>连接串口B</string>
   </property>
   <property name="toolTip">
    <string>连接串口B</string>
   </property>
  </action>
  <action name="actionDisconnectA">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/disconnect.svg</normaloff>
     <normalon>:/icons/disconnect.svg</normalon>:/icons/disconnect.svg</iconset>
   </property>
   <property name="text">
    <string>断开串口A</string>
   </property>
   <property name="toolTip">
    <string>断开串口A</string>
   </property>
  </action>
  <action name="actionDisconnectB">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/disconnect.svg</normaloff>
     <normalon>:/icons/disconnect.svg</normalon>:/icons/disconnect.svg</iconset>
   </property>
   <property name="text">
    <string>断开串口B</string>
   </property>
   <property name="toolTip">
    <string>断开串口B</string>
   </property>
  </action>
  <action name="actionRefresh">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/refresh.svg</normaloff>
     <normalon>:/icons/refresh.svg</normalon>:/icons/refresh.svg</iconset>
   </property>
   <property name="text">
    <string>刷新串口</string>
   </property>
   <property name="toolTip">
    <string>刷新串口</string>
   </property>
  </action>
  <action name="actionGenerateReport">
   <property name="text">
    <string>生成报表</string>
   </property>
  </action>
  <action name="actionViewReport">
   <property name="text">
    <string>查看报表</string>
   </property>
  </action>
  <action name="actionImportConfig">
   <property name="text">
    <string>导入配置</string>
   </property>
  </action>
  <action name="actionExportConfig">
   <property name="text">
    <string>导出配置</string>
   </property>
  </action>
  <action name="actionSetValue">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/setvalue.svg</normaloff>
     <normalon>:/icons/setvalue.svg</normalon>:/icons/setvalue.svg</iconset>
   </property>
   <property name="text">
    <string>定值召唤</string>
   </property>
   <property name="toolTip">
    <string>定值召唤</string>
   </property>
  </action>
  <action name="actionClearSerial">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/clear.svg</normaloff>
     <normalon>:/icons/clear.svg</normalon>:/icons/clear.svg</iconset>
   </property>
   <property name="text">
    <string>清空串口数据</string>
   </property>
   <property name="toolTip">
    <string>清空串口A和串口B的接收数据显示</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
