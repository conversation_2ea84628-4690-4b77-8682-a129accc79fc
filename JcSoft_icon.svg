<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976D2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0D47A1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="circuitGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E3F2FD;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 主背景圆角矩形 -->
  <rect x="16" y="16" width="224" height="224" rx="32" ry="32" fill="url(#bgGradient)" stroke="#0D47A1" stroke-width="2"/>
  
  <!-- 内部装饰边框 -->
  <rect x="24" y="24" width="208" height="208" rx="24" ry="24" fill="none" stroke="#FFFFFF" stroke-width="1" opacity="0.3"/>
  
  <!-- 主要图标元素：电路板风格 -->
  <!-- 中央处理器芯片 -->
  <rect x="96" y="96" width="64" height="64" rx="8" ry="8" fill="url(#circuitGradient)" stroke="#1B5E20" stroke-width="2"/>
  
  <!-- 芯片内部细节 -->
  <rect x="104" y="104" width="48" height="48" rx="4" ry="4" fill="#2E7D32" stroke="#4CAF50" stroke-width="1"/>
  <circle cx="128" cy="128" r="12" fill="#66BB6A"/>
  <circle cx="128" cy="128" r="6" fill="#A5D6A7"/>
  
  <!-- 连接线路 -->
  <!-- 水平线路 -->
  <rect x="40" y="124" width="56" height="8" rx="4" fill="#FF9800"/>
  <rect x="160" y="124" width="56" height="8" rx="4" fill="#FF9800"/>
  
  <!-- 垂直线路 -->
  <rect x="124" y="40" width="8" height="56" rx="4" fill="#FF9800"/>
  <rect x="124" y="160" width="8" height="56" rx="4" fill="#FF9800"/>
  
  <!-- 连接节点 -->
  <circle cx="48" cy="128" r="8" fill="#FFC107" stroke="#FF8F00" stroke-width="2"/>
  <circle cx="208" cy="128" r="8" fill="#FFC107" stroke="#FF8F00" stroke-width="2"/>
  <circle cx="128" cy="48" r="8" fill="#FFC107" stroke="#FF8F00" stroke-width="2"/>
  <circle cx="128" cy="208" r="8" fill="#FFC107" stroke="#FF8F00" stroke-width="2"/>
  
  <!-- 数据传输指示器 -->
  <circle cx="72" cy="128" r="4" fill="#4CAF50">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="184" cy="128" r="4" fill="#4CAF50">
    <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 品牌标识 JC -->
  <text x="128" y="80" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="url(#textGradient)">JC</text>
  
  <!-- 副标题 -->
  <text x="128" y="190" font-family="Arial, sans-serif" font-size="12" font-weight="normal" text-anchor="middle" fill="#E3F2FD" opacity="0.8">MODBUS</text>
  
  <!-- 装饰性元素：角落的小电路 -->
  <!-- 左上角 -->
  <rect x="32" y="32" width="16" height="4" rx="2" fill="#FF9800" opacity="0.6"/>
  <rect x="32" y="40" width="12" height="4" rx="2" fill="#FF9800" opacity="0.6"/>
  <circle cx="52" cy="34" r="2" fill="#FFC107"/>
  
  <!-- 右上角 -->
  <rect x="208" y="32" width="16" height="4" rx="2" fill="#FF9800" opacity="0.6"/>
  <rect x="212" y="40" width="12" height="4" rx="2" fill="#FF9800" opacity="0.6"/>
  <circle cx="204" cy="34" r="2" fill="#FFC107"/>
  
  <!-- 左下角 -->
  <rect x="32" y="220" width="16" height="4" rx="2" fill="#FF9800" opacity="0.6"/>
  <rect x="32" y="212" width="12" height="4" rx="2" fill="#FF9800" opacity="0.6"/>
  <circle cx="52" cy="222" r="2" fill="#FFC107"/>
  
  <!-- 右下角 -->
  <rect x="208" y="220" width="16" height="4" rx="2" fill="#FF9800" opacity="0.6"/>
  <rect x="212" y="212" width="12" height="4" rx="2" fill="#FF9800" opacity="0.6"/>
  <circle cx="204" cy="222" r="2" fill="#FFC107"/>
</svg>
