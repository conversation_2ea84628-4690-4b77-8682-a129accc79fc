#ifndef VALUEFILESELECTIONDIALOG_H
#define VALUEFILESELECTIONDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QLabel>
#include <QComboBox>
#include <QLineEdit>
#include <QPushButton>
#include <QTableWidget>
#include <QCheckBox>
#include <QHeaderView>
#include <QMessageBox>
#include <QAxObject>
#include <QVariant>
#include "setvaluedialog.h"
#include "dataconversionrulesdialog.h"
#include "datamappingrulesdialog.h"

class ValueFileSelectionDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ValueFileSelectionDialog(const QString &excelFilePath, 
                                    const QString &deviceModel,
                                    const QList<ParseItem> &parseItems,
                                    QWidget *parent = nullptr);
    ~ValueFileSelectionDialog();
    
    // 获取更新后的解析项数据
    QList<ParseItem> getUpdatedParseItems() const;
    
    // 获取Excel表格的列名称
    QStringList getExcelHeaders() const;

signals:
    void logMessageGenerated(const QString &message);
    void dataImported(); // 数据导入完成信号

private slots:
    void onWorksheetChanged();
    void onDeviceModelChanged();
    void onMappingRuleChanged();
    void onCabinetNumberChanged();
    void onConfirmClicked();
    void onCancelClicked();
    void onTableDoubleClicked(int row, int column);
    void onDataMappingRulesClicked();
    void onConfigureRulesClicked();

private:
    // UI组件
    QVBoxLayout *mainLayout;
    QFormLayout *formLayout;
    QHBoxLayout *buttonLayout;
    
    QLabel *labelExcelFile;
    QLabel *labelWorksheet;
    QComboBox *comboBoxWorksheet;
    QLabel *labelDeviceModel;
    QComboBox *comboBoxDeviceModel;
    QLabel *labelMappingRule;
    QComboBox *comboBoxMappingRule;
    QLabel *labelCabinetNumber;
    QLineEdit *lineEditCabinetNumber;
    
    // 匹配选项
    QLabel *labelMatchOptions;
    QCheckBox *checkBoxExactMatch;
    QCheckBox *checkBoxPartialMatch;
    QCheckBox *checkBoxChineseMatch;
    QCheckBox *checkBoxUseDisplayValue;
    
    QTableWidget *tableWidgetPreview;
    QPushButton *pushButtonConfirm;
    QPushButton *pushButtonCancel;
    QPushButton *pushButtonDataMappingRules;
    QPushButton *pushButtonConfigureRules;
    
    // 数据成员
    QString excelFilePath;
    QString originalDeviceModel;
    QList<ParseItem> originalParseItems;
    QList<ParseItem> updatedParseItems;
    QStringList excelHeaders;
    QList<QStringList> excelData;
    QList<DataConversionRule> conversionRules;
    QList<DataMappingRule> dataMappingRules;
    QString currentMappingConfigName;
    QStringList worksheetNames;  // 工作表名称列表
    
    // Excel缓存相关
    QAxObject *cachedExcel;
    QAxObject *cachedWorkbook;
    QString cachedFilePath;
    
    // 预览表格优化
    static const int MAX_PREVIEW_ROWS = 100; // 限制预览行数
    
    // 日志控制
    bool enableDebugLog = false;  // 控制调试日志输出
    bool enableUILog = false;     // 控制UI日志输出
    
    // 私有方法
    void setupUI();
    void loadWorksheetNames();
    void loadExcelFile();
    void loadExcelFile(int worksheetIndex);
    void loadExcelFile(const QString &worksheetName);
    void initializeExcelData();  // 优化的初始化函数，一次性加载工作表名称和第一个工作表数据
    void openExcelFile();
    void closeExcelFile();
    void loadWorksheetData(const QString &worksheetName);
    void loadMappingConfigs();
    void updatePreviewTable();
    void matchDataToParseItems();
    bool validateDeviceModel();
    int findCabinetRow(const QString &cabinetNumber);
    void loadConversionRules();
    void applyConversionRules();
};

#endif // VALUEFILESELECTIONDIALOG_H