{"version": "2.0.0", "tasks": [{"label": "build", "type": "shell", "command": "qmake && mingw32-make", "group": {"kind": "build", "isDefault": true}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$gcc"]}, {"label": "clean", "type": "shell", "command": "mingw32-make clean", "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "rebuild", "type": "shell", "command": "mingw32-make clean && qmake && mingw32-make", "group": "build", "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$gcc"]}]}