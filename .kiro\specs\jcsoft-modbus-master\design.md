# JcSoft Modbus Master 设计文档

## 概述

JcSoft是一个基于Qt框架开发的Modbus RTU主站应用，采用模块化设计架构，支持双串口通信、自动化测试、定值召唤等功能。系统采用事件驱动的异步通信模式，提供直观的图形用户界面和强大的配置管理能力。

## 架构

### 整体架构

系统采用分层架构设计：

```
┌─────────────────────────────────────────┐
│              用户界面层                    │
│  MainWindow | MappingWindow | SetValueDialog │
├─────────────────────────────────────────┤
│              业务逻辑层                    │
│  SerialManager | TestController | ConfigManager │
├─────────────────────────────────────────┤
│              通信协议层                    │
│  ModbusSerialBase | ModbusSerialA/B | CrcUtils │
├─────────────────────────────────────────┤
│              系统接口层                    │
│  QSerialPort | QTimer | QFileSystem      │
└─────────────────────────────────────────┘
```

### 核心模块关系

```mermaid
graph TB
    A[MainWindow] --> B[SerialManager]
    A --> C[MappingWindow]
    A --> D[SetValueDialog]
    
    B --> E[ModbusSerialA]
    B --> F[ModbusSerialB]
    
    E --> G[ModbusSerialBase]
    F --> G
    
    C --> H[TestController]
    C --> I[GraphicsScene]
    
    D --> J[ParseItemManager]
    D --> K[ConfigManager]
    
    G --> L[QSerialPort]
    H --> M[QTimer]
    
    N[CrcUtils] --> G
    O[FileManager] --> K
```

## 组件和接口

### 1. 串口管理组件 (SerialManager)

**职责：** 管理双串口连接、配置和数据传输

**接口：**
```cpp
class SerialManager : public QObject {
public:
    bool connectPort(const QString &portId, const QString &portName, 
                    int baudRate, const QString &parity, 
                    int dataBits, const QString &stopBits);
    void disconnectPort(const QString &portId);
    bool sendData(const QString &portId, const QByteArray &data);
    bool isConnected(const QString &portId) const;
    QStringList getAvailablePorts();
    
signals:
    void connectionStateChanged(const QString &portId, bool connected);
    void errorOccurred(const QString &portId, const QString &error);
    void dataReceived(const QString &portId, const QString &message);
    void dataSent(const QString &portId, const QString &message);
};
```

### 2. Modbus通信组件 (ModbusSerialBase)

**职责：** 实现Modbus RTU协议的底层通信

**接口：**
```cpp
class ModbusSerialBase : public QObject {
public:
    bool connectDevice(const QString &portName, int baudRate,
                      QSerialPort::Parity parity,
                      QSerialPort::DataBits dataBits,
                      QSerialPort::StopBits stopBits);
    void disconnectDevice();
    bool sendRawData(const QByteArray &data);
    
protected:
    QString byteArrayToHex(const QByteArray &data);
    void processReceivedData();
    
signals:
    void communicationMessageSent(const QString &message);
    void communicationMessageReceived(const QString &message);
};
```

### 3. 测试配置组件 (MappingWindow)

**职责：** 管理测试映射关系和自动化测试执行

**核心数据结构：**
```cpp
struct MappingRelation {
    QString serialPort;  // "COMA" 或 "COMB"
    QString device;      // 设备名称
    QString deviceType;  // "controller" 或 "protection"
};

struct TestStep {
    QString type;           // 步骤类型
    QString device;         // 设备名称
    QString command;        // 命令内容
    QString expected;       // 预期结果
    QString actual;         // 实际结果
    bool passed;           // 是否通过
    QString notes;         // 备注信息
    QString configType;    // "modbus" or "uart"
    int delay;            // 延时(ms)
    
    // 统一格式字段
    QString sentDataA;
    QString sentDataB;
    QString expectReceiveA;
    QString expectReceiveB;
    QString actualReceiveA;
    QString actualReceiveB;
    QString crc16;
    bool crc16Enabled;
    int stationId;
    int functionCode;
    QString targetSerialPort;
    bool compareEnabled;
};
```

### 4. 定值召唤组件 (SetValueDialog)

**职责：** 管理设备定值的读取、写入和配置

**核心数据结构：**
```cpp
struct ParseItem {
    QString name;              // 名称(英文)
    QString description;       // 描述(中文)
    QString dataType;          // 数据类型
    QString readDirection;     // 读取方向
    int samplePeriod;         // 采集周期(毫秒)
    int deviceTypeId;         // 存储器类型序号
    int deviceAddress;        // 存储器地址
    QString parseMethod;      // 解析方式
    int bitOffset;           // 位偏移量
    int bcdParse;            // 按BCD解析
    QString address;         // 地址
    QString processMethod;   // 处理方式
    double minValue;         // 最小值
    double maxValue;         // 最大值
    QString unit;            // 单位
    double processParam;     // 数据处理参数
    bool featureSelection;   // 特性选择
    bool enableProtection;   // 投退选择
    QString protectionStatus; // 保护投退
    QString expectedData;     // 期望数据
    QString expectedDataFromFile; // 期望数据(定值文件读取)
    QString actualData;       // 实际数据
    QString compareResult;    // 比对结果
};
```

### 5. CRC校验组件 (CrcUtils)

**职责：** 提供CRC16和CRC32校验计算

**接口：**
```cpp
class CrcUtils {
public:
    static quint16 calculateCRC16(const QByteArray &data);
    static QByteArray calculateCRC16Bytes(const QByteArray &data);
    static QByteArray calculateCRC32(const QByteArray &data);
};
```

## 数据模型

### 1. 配置数据模型

**映射配置：**
- 存储格式：JSON/XML
- 文件位置：dev_doc/mapping_config.json
- 包含：串口映射关系、测试步骤配置

**设备配置：**
- 存储格式：CSV/JSON
- 文件位置：dev_doc/{device_model}.csv
- 包含：设备参数、寄存器定义、数据解析规则

**测试配置：**
- 存储格式：JSON
- 文件位置：dev_doc/test_config.json
- 包含：测试步骤、期望结果、执行参数

### 2. 运行时数据模型

**通信状态：**
```cpp
struct CommunicationState {
    QString portId;
    bool connected;
    QString portName;
    int baudRate;
    QSerialPort::Parity parity;
    QSerialPort::DataBits dataBits;
    QSerialPort::StopBits stopBits;
    QDateTime lastActivity;
    int errorCount;
};
```

**测试执行状态：**
```cpp
struct TestExecutionState {
    int currentStepIndex;
    bool isRunning;
    QDateTime startTime;
    QDateTime endTime;
    int totalSteps;
    int passedSteps;
    int failedSteps;
    QList<TestResult> results;
};
```

## 错误处理

### 1. 错误分类

**通信错误：**
- 串口连接失败
- 数据发送超时
- CRC校验失败
- 协议解析错误

**配置错误：**
- 文件不存在
- 格式解析失败
- 参数验证失败
- 路径访问错误

**业务逻辑错误：**
- 测试步骤执行失败
- 数据比较不匹配
- 设备响应异常
- 用户输入无效

### 2. 错误处理策略

**分层错误处理：**
```cpp
// 底层通信错误
class CommunicationException : public std::exception {
    QString errorCode;
    QString errorMessage;
    QString portId;
};

// 业务逻辑错误
class BusinessLogicException : public std::exception {
    QString operation;
    QString context;
    QString suggestion;
};

// 配置错误
class ConfigurationException : public std::exception {
    QString filePath;
    QString configType;
    QString validationError;
};
```

**错误恢复机制：**
- 自动重试机制（通信超时）
- 降级处理（配置文件缺失时使用默认配置）
- 用户交互（错误提示和手动干预选项）
- 日志记录（详细错误信息和上下文）

## 测试策略

### 1. 单元测试

**测试覆盖范围：**
- CRC校验算法正确性
- Modbus帧构建和解析
- 配置文件读写功能
- 数据转换和验证

**测试框架：** Qt Test Framework

### 2. 集成测试

**测试场景：**
- 串口连接和断开流程
- 完整的Modbus通信流程
- 测试配置的导入导出
- 自动化测试执行流程

### 3. 系统测试

**测试环境：**
- 真实硬件设备连接
- 模拟器环境测试
- 不同操作系统兼容性
- 长时间稳定性测试

### 4. 用户验收测试

**测试重点：**
- 用户界面易用性
- 功能完整性验证
- 性能和响应时间
- 错误处理和恢复

## 性能考虑

### 1. 通信性能

**优化策略：**
- 异步通信避免界面阻塞
- 数据缓冲减少频繁IO操作
- 连接池管理多个串口连接
- 超时机制防止无限等待

### 2. 内存管理

**优化措施：**
- 智能指针管理对象生命周期
- 及时释放大数据对象
- 避免内存泄漏和循环引用
- 合理使用Qt的父子对象机制

### 3. 界面响应性

**优化方案：**
- 后台线程处理耗时操作
- 进度指示器提供用户反馈
- 分页显示大量数据
- 延迟加载减少启动时间

## 安全性

### 1. 数据安全

**保护措施：**
- 配置文件访问权限控制
- 敏感数据加密存储
- 输入数据验证和过滤
- 防止SQL注入和路径遍历

### 2. 通信安全

**安全策略：**
- 数据完整性校验（CRC）
- 通信超时防止死锁
- 错误重试次数限制
- 异常情况下的安全断开

### 3. 系统安全

**防护机制：**
- 异常捕获和处理
- 资源使用限制
- 日志记录和审计
- 用户权限管理