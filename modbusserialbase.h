#ifndef MODBUSSERIALBASE_H
#define MODBUSSERIALBASE_H

#include <QObject>
#include <QSerialPort>
#include <QTimer>
#include <QByteArray>

class ModbusSerialBase : public QObject
{
    Q_OBJECT
public:
    explicit ModbusSerialBase(QObject *parent = nullptr);
    virtual ~ModbusSerialBase();

    bool connectDevice(const QString &portName, int baudRate,
                      QSerialPort::Parity parity,
                      QSerialPort::DataBits dataBits,
                      QSerialPort::StopBits stopBits);
    void disconnectDevice();
    bool isConnected() const;
    bool sendRawData(const QByteArray &data);

signals:
    void connectionStateChanged(bool connected);
    void errorOccurred(const QString &error);
    void communicationMessageSent(const QString &message);
    void communicationMessageReceived(const QString &message);

protected:
    QString byteArrayToHex(const QByteArray &data);

private:
    QSerialPort *serialPort;
    QTimer *receiveTimer;
    QByteArray receiveBuffer;
    
    void processReceivedData();
};

#endif // MODBUSSERIALBASE_H