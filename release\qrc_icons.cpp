/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.8.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // setvalue.svg
  0x0,0x0,0x1,0xff,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0xe5,0xae,0x9a,
  0xe5,0x80,0xbc,0xe8,0xae,0xbe,0xe7,0xbd,0xae,0xe5,0x9b,0xbe,0xe6,0xa0,0x87,0x20,
  0x2d,0x2d,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,0x22,0x33,
  0x22,0x20,0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x31,
  0x38,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,0x72,
  0x78,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x36,
  0x37,0x33,0x41,0x42,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,
  0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,
  0x6e,0x65,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x37,0x20,0x38,0x68,0x31,0x30,0x4d,0x37,0x20,0x31,0x32,0x68,0x38,0x4d,
  0x37,0x20,0x31,0x36,0x68,0x36,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,
  0x23,0x36,0x37,0x33,0x41,0x42,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,
  0x22,0x31,0x38,0x22,0x20,0x63,0x79,0x3d,0x22,0x36,0x22,0x20,0x72,0x3d,0x22,0x32,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x46,0x46,0x35,0x37,0x32,0x32,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x46,0x46,0x35,0x37,0x32,0x32,
  0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,
  0x31,0x36,0x20,0x36,0x68,0x34,0x4d,0x31,0x38,0x20,0x34,0x76,0x34,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x31,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,
  0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,
    // test.svg
  0x0,0x0,0x2,0x2a,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0xe6,0xb5,0x8b,
  0xe8,0xaf,0x95,0xe5,0x9b,0xbe,0xe6,0xa0,0x87,0x20,0x2d,0x2d,0x3e,0xa,0x20,0x20,
  0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x39,0x20,0x31,0x32,0x6c,0x32,
  0x20,0x32,0x20,0x34,0x2d,0x34,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,
  0x23,0x34,0x43,0x41,0x46,0x35,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,
  0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,
  0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x32,0x31,0x20,0x31,0x32,0x63,0x30,0x20,0x34,
  0x2e,0x39,0x37,0x2d,0x34,0x2e,0x30,0x33,0x20,0x39,0x2d,0x39,0x20,0x39,0x73,0x2d,
  0x39,0x2d,0x34,0x2e,0x30,0x33,0x2d,0x39,0x2d,0x39,0x20,0x34,0x2e,0x30,0x33,0x2d,
  0x39,0x20,0x39,0x2d,0x39,0x63,0x31,0x2e,0x32,0x34,0x20,0x30,0x20,0x32,0x2e,0x34,
  0x33,0x2e,0x32,0x35,0x20,0x33,0x2e,0x35,0x31,0x2e,0x37,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x3d,0x22,0x23,0x32,0x31,0x39,0x36,0x46,0x33,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,
  0x6f,0x75,0x6e,0x64,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x32,0x32,0x20,0x34,
  0x4c,0x31,0x32,0x20,0x31,0x34,0x2e,0x30,0x31,0x6c,0x2d,0x33,0x2d,0x33,0x22,0x20,
  0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x34,0x43,0x41,0x46,0x35,0x30,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,
  0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,
  0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,
    // refresh.svg
  0x0,0x0,0x2,0x9d,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0xe5,0x88,0xb7,
  0xe6,0x96,0xb0,0xe5,0x9b,0xbe,0xe6,0xa0,0x87,0x20,0x2d,0x2d,0x3e,0xa,0x20,0x20,
  0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x33,0x20,0x31,0x32,0x61,0x39,
  0x20,0x39,0x20,0x30,0x20,0x30,0x20,0x31,0x20,0x39,0x2d,0x39,0x20,0x39,0x2e,0x37,
  0x35,0x20,0x39,0x2e,0x37,0x35,0x20,0x30,0x20,0x30,0x20,0x31,0x20,0x36,0x2e,0x37,
  0x34,0x20,0x32,0x2e,0x37,0x34,0x4c,0x32,0x31,0x20,0x38,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x3d,0x22,0x23,0x32,0x31,0x39,0x36,0x46,0x33,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,
  0x6f,0x75,0x6e,0x64,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x32,0x31,0x20,0x33,
  0x76,0x35,0x68,0x2d,0x35,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,
  0x32,0x31,0x39,0x36,0x46,0x33,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,
  0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x20,
  0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,
  0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,
  0x68,0x20,0x64,0x3d,0x22,0x4d,0x32,0x31,0x20,0x31,0x32,0x61,0x39,0x20,0x39,0x20,
  0x30,0x20,0x30,0x20,0x31,0x2d,0x39,0x20,0x39,0x20,0x39,0x2e,0x37,0x35,0x20,0x39,
  0x2e,0x37,0x35,0x20,0x30,0x20,0x30,0x20,0x31,0x2d,0x36,0x2e,0x37,0x34,0x2d,0x32,
  0x2e,0x37,0x34,0x4c,0x33,0x20,0x31,0x36,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x3d,0x22,0x23,0x32,0x31,0x39,0x36,0x46,0x33,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,
  0x64,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,
  0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x38,0x20,0x31,0x36,0x48,0x33,0x76,
  0x35,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x32,0x31,0x39,0x36,
  0x46,0x33,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,
  0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,
  0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,
    // settings.svg
  0x0,0x0,0x1,0x85,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0xe8,0xae,0xbe,
  0xe7,0xbd,0xae,0xe5,0x9b,0xbe,0xe6,0xa0,0x87,0x20,0x2d,0x2d,0x3e,0xa,0x20,0x20,
  0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x32,0x22,0x20,
  0x63,0x79,0x3d,0x22,0x31,0x32,0x22,0x20,0x72,0x3d,0x22,0x33,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x46,0x46,0x39,0x38,0x30,0x30,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,
  0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x2f,0x3e,0xa,0x20,0x20,
  0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x32,0x20,0x31,0x6c,0x33,
  0x2e,0x30,0x39,0x20,0x36,0x2e,0x32,0x36,0x4c,0x32,0x32,0x20,0x39,0x6c,0x2d,0x35,
  0x2e,0x39,0x31,0x20,0x33,0x2e,0x37,0x34,0x4c,0x31,0x38,0x20,0x31,0x39,0x6c,0x2d,
  0x36,0x2d,0x33,0x2e,0x32,0x37,0x4c,0x36,0x20,0x31,0x39,0x6c,0x31,0x2e,0x39,0x31,
  0x2d,0x36,0x2e,0x32,0x36,0x4c,0x32,0x20,0x39,0x6c,0x36,0x2e,0x39,0x31,0x2d,0x31,
  0x2e,0x37,0x34,0x4c,0x31,0x32,0x20,0x31,0x7a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,
  0x22,0x23,0x46,0x46,0x39,0x38,0x30,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x3d,0x22,0x23,0x46,0x46,0x39,0x38,0x30,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x31,0x22,0x2f,0x3e,0xa,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,
    // connect.svg
  0x0,0x0,0x2,0x1c,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0xe7,0xbd,0x91,
  0xe7,0xbb,0x9c,0xe8,0xbf,0x9e,0xe6,0x8e,0xa5,0xe5,0x9b,0xbe,0xe6,0xa0,0x87,0x20,
  0x2d,0x2d,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,
  0x3d,0x22,0x31,0x38,0x22,0x20,0x63,0x79,0x3d,0x22,0x35,0x22,0x20,0x72,0x3d,0x22,
  0x33,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x34,0x43,0x41,0x46,0x35,0x30,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x34,0x43,0x41,0x46,0x35,
  0x30,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,
  0x78,0x3d,0x22,0x36,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x32,0x22,0x20,0x72,0x3d,
  0x22,0x33,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x34,0x43,0x41,0x46,0x35,
  0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x34,0x43,0x41,0x46,
  0x35,0x30,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,
  0x63,0x78,0x3d,0x22,0x31,0x38,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x39,0x22,0x20,
  0x72,0x3d,0x22,0x33,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x34,0x43,0x41,
  0x46,0x35,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x34,0x43,
  0x41,0x46,0x35,0x30,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x6c,0x69,0x6e,0x65,0x20,
  0x78,0x31,0x3d,0x22,0x38,0x2e,0x35,0x39,0x22,0x20,0x79,0x31,0x3d,0x22,0x31,0x33,
  0x2e,0x35,0x31,0x22,0x20,0x78,0x32,0x3d,0x22,0x31,0x35,0x2e,0x34,0x32,0x22,0x20,
  0x79,0x32,0x3d,0x22,0x31,0x37,0x2e,0x34,0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x3d,0x22,0x23,0x32,0x31,0x39,0x36,0x46,0x33,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x2f,0x3e,0xa,0x20,
  0x20,0x3c,0x6c,0x69,0x6e,0x65,0x20,0x78,0x31,0x3d,0x22,0x31,0x35,0x2e,0x34,0x31,
  0x22,0x20,0x79,0x31,0x3d,0x22,0x36,0x2e,0x35,0x31,0x22,0x20,0x78,0x32,0x3d,0x22,
  0x38,0x2e,0x35,0x39,0x22,0x20,0x79,0x32,0x3d,0x22,0x31,0x30,0x2e,0x34,0x39,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x32,0x31,0x39,0x36,0x46,0x33,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,
    // clear.svg
  0x0,0x0,0x2,0xb,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0xe6,0xb8,0x85,
  0xe7,0xa9,0xba,0xe5,0x9b,0xbe,0xe6,0xa0,0x87,0x20,0x2d,0x20,0xe5,0x9e,0x83,0xe5,
  0x9c,0xbe,0xe6,0xa1,0xb6,0xe6,0xa0,0xb7,0xe5,0xbc,0x8f,0x20,0x2d,0x2d,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x33,0x20,0x36,0x68,
  0x31,0x38,0x6c,0x2d,0x31,0x2e,0x35,0x20,0x31,0x34,0x48,0x34,0x2e,0x35,0x4c,0x33,
  0x20,0x36,0x7a,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x33,0x33,
  0x33,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x32,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x2f,
  0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x38,0x20,
  0x36,0x56,0x34,0x61,0x31,0x20,0x31,0x20,0x30,0x20,0x30,0x31,0x31,0x2d,0x31,0x68,
  0x36,0x61,0x31,0x20,0x31,0x20,0x30,0x20,0x30,0x31,0x31,0x20,0x31,0x76,0x32,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x33,0x33,0x33,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,
  0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x2f,0x3e,0xa,0x20,0x20,
  0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x35,0x20,0x36,0x68,0x31,0x34,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x33,0x33,0x33,0x22,0x20,
  0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,
  0x30,0x20,0x31,0x31,0x76,0x36,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,
  0x23,0x33,0x33,0x33,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,
  0x74,0x68,0x3d,0x22,0x31,0x2e,0x35,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,
  0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x34,0x20,0x31,0x31,0x76,0x36,0x22,0x20,
  0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x33,0x33,0x33,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x31,0x2e,0x35,0x22,
  0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // disconnect.svg
  0x0,0x0,0x2,0x94,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0xe6,0x96,0xad,
  0xe5,0xbc,0x80,0xe8,0xbf,0x9e,0xe6,0x8e,0xa5,0xe5,0x9b,0xbe,0xe6,0xa0,0x87,0x20,
  0x2d,0x2d,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,
  0x3d,0x22,0x31,0x38,0x22,0x20,0x63,0x79,0x3d,0x22,0x35,0x22,0x20,0x72,0x3d,0x22,
  0x33,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x46,0x46,0x35,0x37,0x32,0x32,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x46,0x46,0x35,0x37,0x32,
  0x32,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,
  0x78,0x3d,0x22,0x36,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x32,0x22,0x20,0x72,0x3d,
  0x22,0x33,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x46,0x46,0x35,0x37,0x32,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x46,0x46,0x35,0x37,
  0x32,0x32,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,
  0x63,0x78,0x3d,0x22,0x31,0x38,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x39,0x22,0x20,
  0x72,0x3d,0x22,0x33,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x46,0x46,0x35,
  0x37,0x32,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x46,0x46,
  0x35,0x37,0x32,0x32,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x6c,0x69,0x6e,0x65,0x20,
  0x78,0x31,0x3d,0x22,0x38,0x2e,0x35,0x39,0x22,0x20,0x79,0x31,0x3d,0x22,0x31,0x33,
  0x2e,0x35,0x31,0x22,0x20,0x78,0x32,0x3d,0x22,0x31,0x35,0x2e,0x34,0x32,0x22,0x20,
  0x79,0x32,0x3d,0x22,0x31,0x37,0x2e,0x34,0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x3d,0x22,0x23,0x46,0x46,0x35,0x37,0x32,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x2d,0x64,0x61,0x73,0x68,0x61,0x72,0x72,0x61,0x79,0x3d,0x22,0x33,
  0x2c,0x33,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x6c,0x69,0x6e,0x65,0x20,0x78,0x31,
  0x3d,0x22,0x31,0x35,0x2e,0x34,0x31,0x22,0x20,0x79,0x31,0x3d,0x22,0x36,0x2e,0x35,
  0x31,0x22,0x20,0x78,0x32,0x3d,0x22,0x38,0x2e,0x35,0x39,0x22,0x20,0x79,0x32,0x3d,
  0x22,0x31,0x30,0x2e,0x34,0x39,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,
  0x23,0x46,0x46,0x35,0x37,0x32,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x64,0x61,0x73,0x68,0x61,0x72,0x72,0x61,0x79,0x3d,0x22,0x33,0x2c,0x33,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x6c,0x69,0x6e,0x65,0x20,0x78,0x31,0x3d,0x22,0x33,
  0x22,0x20,0x79,0x31,0x3d,0x22,0x33,0x22,0x20,0x78,0x32,0x3d,0x22,0x32,0x31,0x22,
  0x20,0x79,0x32,0x3d,0x22,0x32,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,
  0x22,0x23,0x46,0x34,0x34,0x33,0x33,0x36,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,
  0x76,0x67,0x3e,
    // modbus.svg
  0x0,0x0,0x2,0x34,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0x4d,0x6f,0x64,
  0x62,0x75,0x73,0xe9,0x80,0x9a,0xe4,0xbf,0xa1,0xe5,0x9b,0xbe,0xe6,0xa0,0x87,0x20,
  0x2d,0x2d,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,0x22,0x32,
  0x22,0x20,0x79,0x3d,0x22,0x34,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x30,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x36,0x22,0x20,0x72,
  0x78,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x36,
  0x30,0x37,0x44,0x38,0x42,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,
  0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,
  0x6e,0x65,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x36,0x20,0x38,0x68,0x31,0x32,0x4d,0x36,0x20,0x31,0x32,0x68,0x38,0x4d,
  0x36,0x20,0x31,0x36,0x68,0x31,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,
  0x22,0x23,0x36,0x30,0x37,0x44,0x38,0x42,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x31,0x2e,0x35,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,
  0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,
  0x63,0x78,0x3d,0x22,0x31,0x38,0x22,0x20,0x63,0x79,0x3d,0x22,0x38,0x22,0x20,0x72,
  0x3d,0x22,0x31,0x2e,0x35,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x34,0x43,
  0x41,0x46,0x35,0x30,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,
  0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x36,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x32,
  0x22,0x20,0x72,0x3d,0x22,0x31,0x2e,0x35,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x23,0x46,0x46,0x39,0x38,0x30,0x30,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x38,0x22,0x20,0x63,0x79,0x3d,
  0x22,0x31,0x36,0x22,0x20,0x72,0x3d,0x22,0x31,0x2e,0x35,0x22,0x20,0x66,0x69,0x6c,
  0x6c,0x3d,0x22,0x23,0x32,0x31,0x39,0x36,0x46,0x33,0x22,0x2f,0x3e,0xa,0x20,0x20,
  0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x32,0x20,0x31,0x30,0x6c,0x34,
  0x2d,0x32,0x76,0x34,0x6c,0x2d,0x34,0x2d,0x32,0x7a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x23,0x36,0x30,0x37,0x44,0x38,0x42,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,
  0x76,0x67,0x3e,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // setvalue.svg
  0x0,0xc,
  0xc,0x91,0x23,0xc7,
  0x0,0x73,
  0x0,0x65,0x0,0x74,0x0,0x76,0x0,0x61,0x0,0x6c,0x0,0x75,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // test.svg
  0x0,0x8,
  0xc,0xa7,0x55,0x87,
  0x0,0x74,
  0x0,0x65,0x0,0x73,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // refresh.svg
  0x0,0xb,
  0xc,0x6a,0x21,0xc7,
  0x0,0x72,
  0x0,0x65,0x0,0x66,0x0,0x72,0x0,0x65,0x0,0x73,0x0,0x68,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // settings.svg
  0x0,0xc,
  0xb,0xdf,0x2c,0xc7,
  0x0,0x73,
  0x0,0x65,0x0,0x74,0x0,0x74,0x0,0x69,0x0,0x6e,0x0,0x67,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // connect.svg
  0x0,0xb,
  0xb,0x73,0x90,0x47,
  0x0,0x63,
  0x0,0x6f,0x0,0x6e,0x0,0x6e,0x0,0x65,0x0,0x63,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // clear.svg
  0x0,0x9,
  0xb,0x85,0x8e,0x87,
  0x0,0x63,
  0x0,0x6c,0x0,0x65,0x0,0x61,0x0,0x72,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // disconnect.svg
  0x0,0xe,
  0xa,0x93,0x8a,0x87,
  0x0,0x64,
  0x0,0x69,0x0,0x73,0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x6e,0x0,0x65,0x0,0x63,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // modbus.svg
  0x0,0xa,
  0x9,0xcc,0xd1,0x87,
  0x0,0x6d,
  0x0,0x6f,0x0,0x64,0x0,0x62,0x0,0x75,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x8,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons/modbus.svg
  0x0,0x0,0x0,0xd4,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xf,0x22,
0x0,0x0,0x1,0x97,0xd4,0x63,0xf,0x1c,
  // :/icons/disconnect.svg
  0x0,0x0,0x0,0xb2,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xc,0x8a,
0x0,0x0,0x1,0x97,0xd4,0x60,0xcf,0x96,
  // :/icons/connect.svg
  0x0,0x0,0x0,0x7e,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x8,0x5b,
0x0,0x0,0x1,0x97,0xd4,0x60,0x87,0x19,
  // :/icons/clear.svg
  0x0,0x0,0x0,0x9a,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xa,0x7b,
0x0,0x0,0x1,0x97,0xd9,0x40,0x82,0xeb,
  // :/icons/settings.svg
  0x0,0x0,0x0,0x60,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x6,0xd2,
0x0,0x0,0x1,0x97,0xd4,0x61,0xb9,0xea,
  // :/icons/refresh.svg
  0x0,0x0,0x0,0x44,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x4,0x31,
0x0,0x0,0x1,0x97,0xd4,0x61,0x2b,0xe6,
  // :/icons/setvalue.svg
  0x0,0x0,0x0,0x10,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xd4,0x62,0xb1,0xaa,
  // :/icons/test.svg
  0x0,0x0,0x0,0x2e,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x2,0x3,
0x0,0x0,0x1,0x97,0xd4,0x62,0x48,0xd8,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_icons)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_icons)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icons)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icons)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_icons)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icons)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
