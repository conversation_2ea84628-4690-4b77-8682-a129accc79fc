/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 6.8.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtGui/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenu>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QAction *actionExit;
    QAction *actionStartTest;
    QAction *actionStopTest;
    QAction *actionMapping;
    QAction *actionConnectA;
    QAction *actionConnectB;
    QAction *actionDisconnectA;
    QAction *actionDisconnectB;
    QAction *actionRefresh;
    QAction *actionGenerateReport;
    QAction *actionViewReport;
    QAction *actionImportConfig;
    QAction *actionExportConfig;
    QAction *actionSetValue;
    QAction *actionClearSerial;
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayoutTop;
    QGroupBox *groupBoxSerialA;
    QGridLayout *gridLayoutA;
    QLabel *labelPortA;
    QComboBox *comboBoxPortA;
    QLabel *labelBaudA;
    QComboBox *comboBoxBaudA;
    QLabel *labelParityA;
    QComboBox *comboBoxParityA;
    QLabel *labelDataA;
    QComboBox *comboBoxDataA;
    QLabel *labelStopA;
    QComboBox *comboBoxStopA;
    QGroupBox *groupBoxSerialB;
    QGridLayout *gridLayoutB;
    QLabel *labelPortB;
    QComboBox *comboBoxPortB;
    QLabel *labelBaudB;
    QComboBox *comboBoxBaudB;
    QLabel *labelParityB;
    QComboBox *comboBoxParityB;
    QLabel *labelDataB;
    QComboBox *comboBoxDataB;
    QLabel *labelStopB;
    QComboBox *comboBoxStopB;
    QHBoxLayout *horizontalLayoutBottom;
    QVBoxLayout *verticalLayoutA;
    QGroupBox *groupBoxSendA;
    QHBoxLayout *horizontalLayoutSendA;
    QLineEdit *lineEditSendDataA;
    QPushButton *pushButtonSendA;
    QGroupBox *groupBoxDisplayA;
    QVBoxLayout *verticalLayoutDisplayA;
    QTextBrowser *textBrowserA;
    QVBoxLayout *verticalLayoutB;
    QGroupBox *groupBoxSendB;
    QHBoxLayout *horizontalLayoutSendB;
    QLineEdit *lineEditSendDataB;
    QPushButton *pushButtonSendB;
    QGroupBox *groupBoxDisplayB;
    QVBoxLayout *verticalLayoutDisplayB;
    QTextBrowser *textBrowserB;
    QToolBar *mainToolBar;
    QMenuBar *menubar;
    QMenu *menuFile;
    QMenu *menuTest;
    QMenu *menuConfig;
    QMenu *menuReport;
    QMenu *menuTools;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName("MainWindow");
        MainWindow->resize(1382, 800);
        actionExit = new QAction(MainWindow);
        actionExit->setObjectName("actionExit");
        actionStartTest = new QAction(MainWindow);
        actionStartTest->setObjectName("actionStartTest");
        actionStopTest = new QAction(MainWindow);
        actionStopTest->setObjectName("actionStopTest");
        actionMapping = new QAction(MainWindow);
        actionMapping->setObjectName("actionMapping");
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/test.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        icon.addFile(QString::fromUtf8(":/icons/test.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::On);
        actionMapping->setIcon(icon);
        actionConnectA = new QAction(MainWindow);
        actionConnectA->setObjectName("actionConnectA");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/connect.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        icon1.addFile(QString::fromUtf8(":/icons/connect.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::On);
        actionConnectA->setIcon(icon1);
        actionConnectB = new QAction(MainWindow);
        actionConnectB->setObjectName("actionConnectB");
        actionConnectB->setIcon(icon1);
        actionDisconnectA = new QAction(MainWindow);
        actionDisconnectA->setObjectName("actionDisconnectA");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/disconnect.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        icon2.addFile(QString::fromUtf8(":/icons/disconnect.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::On);
        actionDisconnectA->setIcon(icon2);
        actionDisconnectB = new QAction(MainWindow);
        actionDisconnectB->setObjectName("actionDisconnectB");
        actionDisconnectB->setIcon(icon2);
        actionRefresh = new QAction(MainWindow);
        actionRefresh->setObjectName("actionRefresh");
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/icons/refresh.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        icon3.addFile(QString::fromUtf8(":/icons/refresh.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::On);
        actionRefresh->setIcon(icon3);
        actionGenerateReport = new QAction(MainWindow);
        actionGenerateReport->setObjectName("actionGenerateReport");
        actionViewReport = new QAction(MainWindow);
        actionViewReport->setObjectName("actionViewReport");
        actionImportConfig = new QAction(MainWindow);
        actionImportConfig->setObjectName("actionImportConfig");
        actionExportConfig = new QAction(MainWindow);
        actionExportConfig->setObjectName("actionExportConfig");
        actionSetValue = new QAction(MainWindow);
        actionSetValue->setObjectName("actionSetValue");
        QIcon icon4;
        icon4.addFile(QString::fromUtf8(":/icons/setvalue.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        icon4.addFile(QString::fromUtf8(":/icons/setvalue.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::On);
        actionSetValue->setIcon(icon4);
        actionClearSerial = new QAction(MainWindow);
        actionClearSerial->setObjectName("actionClearSerial");
        QIcon icon5;
        icon5.addFile(QString::fromUtf8(":/icons/clear.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        icon5.addFile(QString::fromUtf8(":/icons/clear.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::On);
        actionClearSerial->setIcon(icon5);
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName("centralwidget");
        verticalLayout = new QVBoxLayout(centralwidget);
        verticalLayout->setObjectName("verticalLayout");
        horizontalLayoutTop = new QHBoxLayout();
        horizontalLayoutTop->setObjectName("horizontalLayoutTop");
        groupBoxSerialA = new QGroupBox(centralwidget);
        groupBoxSerialA->setObjectName("groupBoxSerialA");
        gridLayoutA = new QGridLayout(groupBoxSerialA);
        gridLayoutA->setObjectName("gridLayoutA");
        labelPortA = new QLabel(groupBoxSerialA);
        labelPortA->setObjectName("labelPortA");

        gridLayoutA->addWidget(labelPortA, 0, 0, 1, 1);

        comboBoxPortA = new QComboBox(groupBoxSerialA);
        comboBoxPortA->setObjectName("comboBoxPortA");

        gridLayoutA->addWidget(comboBoxPortA, 0, 1, 1, 1);

        labelBaudA = new QLabel(groupBoxSerialA);
        labelBaudA->setObjectName("labelBaudA");

        gridLayoutA->addWidget(labelBaudA, 0, 2, 1, 1);

        comboBoxBaudA = new QComboBox(groupBoxSerialA);
        comboBoxBaudA->setObjectName("comboBoxBaudA");

        gridLayoutA->addWidget(comboBoxBaudA, 0, 3, 1, 1);

        labelParityA = new QLabel(groupBoxSerialA);
        labelParityA->setObjectName("labelParityA");

        gridLayoutA->addWidget(labelParityA, 0, 4, 1, 1);

        comboBoxParityA = new QComboBox(groupBoxSerialA);
        comboBoxParityA->setObjectName("comboBoxParityA");

        gridLayoutA->addWidget(comboBoxParityA, 0, 5, 1, 1);

        labelDataA = new QLabel(groupBoxSerialA);
        labelDataA->setObjectName("labelDataA");

        gridLayoutA->addWidget(labelDataA, 0, 6, 1, 1);

        comboBoxDataA = new QComboBox(groupBoxSerialA);
        comboBoxDataA->setObjectName("comboBoxDataA");

        gridLayoutA->addWidget(comboBoxDataA, 0, 7, 1, 1);

        labelStopA = new QLabel(groupBoxSerialA);
        labelStopA->setObjectName("labelStopA");

        gridLayoutA->addWidget(labelStopA, 0, 8, 1, 1);

        comboBoxStopA = new QComboBox(groupBoxSerialA);
        comboBoxStopA->setObjectName("comboBoxStopA");

        gridLayoutA->addWidget(comboBoxStopA, 0, 9, 1, 1);


        horizontalLayoutTop->addWidget(groupBoxSerialA);

        groupBoxSerialB = new QGroupBox(centralwidget);
        groupBoxSerialB->setObjectName("groupBoxSerialB");
        gridLayoutB = new QGridLayout(groupBoxSerialB);
        gridLayoutB->setObjectName("gridLayoutB");
        labelPortB = new QLabel(groupBoxSerialB);
        labelPortB->setObjectName("labelPortB");

        gridLayoutB->addWidget(labelPortB, 0, 0, 1, 1);

        comboBoxPortB = new QComboBox(groupBoxSerialB);
        comboBoxPortB->setObjectName("comboBoxPortB");

        gridLayoutB->addWidget(comboBoxPortB, 0, 1, 1, 1);

        labelBaudB = new QLabel(groupBoxSerialB);
        labelBaudB->setObjectName("labelBaudB");

        gridLayoutB->addWidget(labelBaudB, 0, 2, 1, 1);

        comboBoxBaudB = new QComboBox(groupBoxSerialB);
        comboBoxBaudB->setObjectName("comboBoxBaudB");

        gridLayoutB->addWidget(comboBoxBaudB, 0, 3, 1, 1);

        labelParityB = new QLabel(groupBoxSerialB);
        labelParityB->setObjectName("labelParityB");

        gridLayoutB->addWidget(labelParityB, 0, 4, 1, 1);

        comboBoxParityB = new QComboBox(groupBoxSerialB);
        comboBoxParityB->setObjectName("comboBoxParityB");

        gridLayoutB->addWidget(comboBoxParityB, 0, 5, 1, 1);

        labelDataB = new QLabel(groupBoxSerialB);
        labelDataB->setObjectName("labelDataB");

        gridLayoutB->addWidget(labelDataB, 0, 6, 1, 1);

        comboBoxDataB = new QComboBox(groupBoxSerialB);
        comboBoxDataB->setObjectName("comboBoxDataB");

        gridLayoutB->addWidget(comboBoxDataB, 0, 7, 1, 1);

        labelStopB = new QLabel(groupBoxSerialB);
        labelStopB->setObjectName("labelStopB");

        gridLayoutB->addWidget(labelStopB, 0, 8, 1, 1);

        comboBoxStopB = new QComboBox(groupBoxSerialB);
        comboBoxStopB->setObjectName("comboBoxStopB");

        gridLayoutB->addWidget(comboBoxStopB, 0, 9, 1, 1);


        horizontalLayoutTop->addWidget(groupBoxSerialB);


        verticalLayout->addLayout(horizontalLayoutTop);

        horizontalLayoutBottom = new QHBoxLayout();
        horizontalLayoutBottom->setObjectName("horizontalLayoutBottom");
        verticalLayoutA = new QVBoxLayout();
        verticalLayoutA->setObjectName("verticalLayoutA");
        groupBoxSendA = new QGroupBox(centralwidget);
        groupBoxSendA->setObjectName("groupBoxSendA");
        horizontalLayoutSendA = new QHBoxLayout(groupBoxSendA);
        horizontalLayoutSendA->setObjectName("horizontalLayoutSendA");
        lineEditSendDataA = new QLineEdit(groupBoxSendA);
        lineEditSendDataA->setObjectName("lineEditSendDataA");

        horizontalLayoutSendA->addWidget(lineEditSendDataA);

        pushButtonSendA = new QPushButton(groupBoxSendA);
        pushButtonSendA->setObjectName("pushButtonSendA");

        horizontalLayoutSendA->addWidget(pushButtonSendA);


        verticalLayoutA->addWidget(groupBoxSendA);

        groupBoxDisplayA = new QGroupBox(centralwidget);
        groupBoxDisplayA->setObjectName("groupBoxDisplayA");
        verticalLayoutDisplayA = new QVBoxLayout(groupBoxDisplayA);
        verticalLayoutDisplayA->setObjectName("verticalLayoutDisplayA");
        textBrowserA = new QTextBrowser(groupBoxDisplayA);
        textBrowserA->setObjectName("textBrowserA");

        verticalLayoutDisplayA->addWidget(textBrowserA);


        verticalLayoutA->addWidget(groupBoxDisplayA);


        horizontalLayoutBottom->addLayout(verticalLayoutA);

        verticalLayoutB = new QVBoxLayout();
        verticalLayoutB->setObjectName("verticalLayoutB");
        groupBoxSendB = new QGroupBox(centralwidget);
        groupBoxSendB->setObjectName("groupBoxSendB");
        horizontalLayoutSendB = new QHBoxLayout(groupBoxSendB);
        horizontalLayoutSendB->setObjectName("horizontalLayoutSendB");
        lineEditSendDataB = new QLineEdit(groupBoxSendB);
        lineEditSendDataB->setObjectName("lineEditSendDataB");

        horizontalLayoutSendB->addWidget(lineEditSendDataB);

        pushButtonSendB = new QPushButton(groupBoxSendB);
        pushButtonSendB->setObjectName("pushButtonSendB");

        horizontalLayoutSendB->addWidget(pushButtonSendB);


        verticalLayoutB->addWidget(groupBoxSendB);

        groupBoxDisplayB = new QGroupBox(centralwidget);
        groupBoxDisplayB->setObjectName("groupBoxDisplayB");
        verticalLayoutDisplayB = new QVBoxLayout(groupBoxDisplayB);
        verticalLayoutDisplayB->setObjectName("verticalLayoutDisplayB");
        textBrowserB = new QTextBrowser(groupBoxDisplayB);
        textBrowserB->setObjectName("textBrowserB");

        verticalLayoutDisplayB->addWidget(textBrowserB);


        verticalLayoutB->addWidget(groupBoxDisplayB);


        horizontalLayoutBottom->addLayout(verticalLayoutB);


        verticalLayout->addLayout(horizontalLayoutBottom);

        MainWindow->setCentralWidget(centralwidget);
        mainToolBar = new QToolBar(MainWindow);
        mainToolBar->setObjectName("mainToolBar");
        MainWindow->addToolBar(Qt::ToolBarArea::TopToolBarArea, mainToolBar);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName("menubar");
        menubar->setGeometry(QRect(0, 0, 1382, 20));
        menuFile = new QMenu(menubar);
        menuFile->setObjectName("menuFile");
        menuTest = new QMenu(menubar);
        menuTest->setObjectName("menuTest");
        menuConfig = new QMenu(menubar);
        menuConfig->setObjectName("menuConfig");
        menuReport = new QMenu(menubar);
        menuReport->setObjectName("menuReport");
        menuTools = new QMenu(menubar);
        menuTools->setObjectName("menuTools");
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName("statusbar");
        MainWindow->setStatusBar(statusbar);

        mainToolBar->addAction(actionConnectA);
        mainToolBar->addAction(actionConnectB);
        mainToolBar->addAction(actionDisconnectA);
        mainToolBar->addAction(actionDisconnectB);
        mainToolBar->addAction(actionRefresh);
        mainToolBar->addAction(actionClearSerial);
        mainToolBar->addSeparator();
        mainToolBar->addAction(actionMapping);
        mainToolBar->addAction(actionSetValue);
        menubar->addAction(menuFile->menuAction());
        menubar->addAction(menuConfig->menuAction());
        menubar->addAction(menuTest->menuAction());
        menubar->addAction(menuReport->menuAction());
        menubar->addAction(menuTools->menuAction());
        menuFile->addAction(actionImportConfig);
        menuFile->addAction(actionExportConfig);
        menuFile->addSeparator();
        menuFile->addAction(actionExit);
        menuTest->addAction(actionStartTest);
        menuTest->addAction(actionStopTest);
        menuTest->addSeparator();
        menuConfig->addAction(actionMapping);
        menuConfig->addSeparator();
        menuConfig->addAction(actionSetValue);
        menuReport->addAction(actionGenerateReport);
        menuReport->addAction(actionViewReport);
        menuTools->addAction(actionRefresh);
        menuTools->addSeparator();
        menuTools->addAction(actionConnectA);
        menuTools->addAction(actionConnectB);
        menuTools->addAction(actionDisconnectA);
        menuTools->addAction(actionDisconnectB);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "JcSoft - \345\217\214\344\270\262\345\217\243\351\200\232\344\277\241", nullptr));
        actionExit->setText(QCoreApplication::translate("MainWindow", "\351\200\200\345\207\272", nullptr));
        actionStartTest->setText(QCoreApplication::translate("MainWindow", "\345\274\200\345\247\213\346\265\213\350\257\225", nullptr));
        actionStopTest->setText(QCoreApplication::translate("MainWindow", "\345\201\234\346\255\242\346\265\213\350\257\225", nullptr));
        actionMapping->setText(QCoreApplication::translate("MainWindow", "TEST\351\205\215\347\275\256", nullptr));
#if QT_CONFIG(tooltip)
        actionMapping->setToolTip(QCoreApplication::translate("MainWindow", "TEST\351\205\215\347\275\256", nullptr));
#endif // QT_CONFIG(tooltip)
        actionConnectA->setText(QCoreApplication::translate("MainWindow", "\350\277\236\346\216\245\344\270\262\345\217\243A", nullptr));
#if QT_CONFIG(tooltip)
        actionConnectA->setToolTip(QCoreApplication::translate("MainWindow", "\350\277\236\346\216\245\344\270\262\345\217\243A", nullptr));
#endif // QT_CONFIG(tooltip)
        actionConnectB->setText(QCoreApplication::translate("MainWindow", "\350\277\236\346\216\245\344\270\262\345\217\243B", nullptr));
#if QT_CONFIG(tooltip)
        actionConnectB->setToolTip(QCoreApplication::translate("MainWindow", "\350\277\236\346\216\245\344\270\262\345\217\243B", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDisconnectA->setText(QCoreApplication::translate("MainWindow", "\346\226\255\345\274\200\344\270\262\345\217\243A", nullptr));
#if QT_CONFIG(tooltip)
        actionDisconnectA->setToolTip(QCoreApplication::translate("MainWindow", "\346\226\255\345\274\200\344\270\262\345\217\243A", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDisconnectB->setText(QCoreApplication::translate("MainWindow", "\346\226\255\345\274\200\344\270\262\345\217\243B", nullptr));
#if QT_CONFIG(tooltip)
        actionDisconnectB->setToolTip(QCoreApplication::translate("MainWindow", "\346\226\255\345\274\200\344\270\262\345\217\243B", nullptr));
#endif // QT_CONFIG(tooltip)
        actionRefresh->setText(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260\344\270\262\345\217\243", nullptr));
#if QT_CONFIG(tooltip)
        actionRefresh->setToolTip(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260\344\270\262\345\217\243", nullptr));
#endif // QT_CONFIG(tooltip)
        actionGenerateReport->setText(QCoreApplication::translate("MainWindow", "\347\224\237\346\210\220\346\212\245\350\241\250", nullptr));
        actionViewReport->setText(QCoreApplication::translate("MainWindow", "\346\237\245\347\234\213\346\212\245\350\241\250", nullptr));
        actionImportConfig->setText(QCoreApplication::translate("MainWindow", "\345\257\274\345\205\245\351\205\215\347\275\256", nullptr));
        actionExportConfig->setText(QCoreApplication::translate("MainWindow", "\345\257\274\345\207\272\351\205\215\347\275\256", nullptr));
        actionSetValue->setText(QCoreApplication::translate("MainWindow", "\345\256\232\345\200\274\345\217\254\345\224\244", nullptr));
#if QT_CONFIG(tooltip)
        actionSetValue->setToolTip(QCoreApplication::translate("MainWindow", "\345\256\232\345\200\274\345\217\254\345\224\244", nullptr));
#endif // QT_CONFIG(tooltip)
        actionClearSerial->setText(QCoreApplication::translate("MainWindow", "\346\270\205\347\251\272\344\270\262\345\217\243\346\225\260\346\215\256", nullptr));
#if QT_CONFIG(tooltip)
        actionClearSerial->setToolTip(QCoreApplication::translate("MainWindow", "\346\270\205\347\251\272\344\270\262\345\217\243A\345\222\214\344\270\262\345\217\243B\347\232\204\346\216\245\346\224\266\346\225\260\346\215\256\346\230\276\347\244\272", nullptr));
#endif // QT_CONFIG(tooltip)
        groupBoxSerialA->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243A\351\205\215\347\275\256", nullptr));
        labelPortA->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243\345\217\267\357\274\232", nullptr));
        labelBaudA->setText(QCoreApplication::translate("MainWindow", "\346\263\242\347\211\271\347\216\207\357\274\232", nullptr));
        labelParityA->setText(QCoreApplication::translate("MainWindow", "\346\240\241\351\252\214\344\275\215\357\274\232", nullptr));
        labelDataA->setText(QCoreApplication::translate("MainWindow", "\346\225\260\346\215\256\344\275\215\357\274\232", nullptr));
        labelStopA->setText(QCoreApplication::translate("MainWindow", "\345\201\234\346\255\242\344\275\215\357\274\232", nullptr));
        groupBoxSerialB->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243B\351\205\215\347\275\256", nullptr));
        labelPortB->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243\345\217\267\357\274\232", nullptr));
        labelBaudB->setText(QCoreApplication::translate("MainWindow", "\346\263\242\347\211\271\347\216\207\357\274\232", nullptr));
        labelParityB->setText(QCoreApplication::translate("MainWindow", "\346\240\241\351\252\214\344\275\215\357\274\232", nullptr));
        labelDataB->setText(QCoreApplication::translate("MainWindow", "\346\225\260\346\215\256\344\275\215\357\274\232", nullptr));
        labelStopB->setText(QCoreApplication::translate("MainWindow", "\345\201\234\346\255\242\344\275\215\357\274\232", nullptr));
        groupBoxSendA->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243A\345\217\221\351\200\201\346\225\260\346\215\256", nullptr));
        lineEditSendDataA->setPlaceholderText(QCoreApplication::translate("MainWindow", "\350\257\267\350\276\223\345\205\245\345\215\201\345\205\255\350\277\233\345\210\266\346\225\260\346\215\256\357\274\214\344\276\213\345\246\202\357\274\23201 03 00 00 00 01", nullptr));
        pushButtonSendA->setText(QCoreApplication::translate("MainWindow", "\345\217\221\351\200\201", nullptr));
        groupBoxDisplayA->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243A\346\225\260\346\215\256\346\230\276\347\244\272", nullptr));
        groupBoxDisplayA->setStyleSheet(QCoreApplication::translate("MainWindow", "QGroupBox {\n"
"    font-weight: bold;\n"
"    border: 2px solid #2196F3;\n"
"    border-radius: 8px;\n"
"    margin-top: 1ex;\n"
"    padding-top: 10px;\n"
"    background-color: #f8f9fa;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 8px 0 8px;\n"
"    color: #2196F3;\n"
"}", nullptr));
        textBrowserA->setStyleSheet(QCoreApplication::translate("MainWindow", "QTextBrowser {\n"
"    border: 2px solid #e0e0e0;\n"
"    border-radius: 6px;\n"
"    background-color: #fafafa;\n"
"    font-family: 'Consolas', 'Monaco', monospace;\n"
"    font-size: 10pt;\n"
"    padding: 8px;\n"
"}\n"
"QTextBrowser:focus {\n"
"    border-color: #2196F3;\n"
"}", nullptr));
        groupBoxSendB->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243B\345\217\221\351\200\201\346\225\260\346\215\256", nullptr));
        lineEditSendDataB->setPlaceholderText(QCoreApplication::translate("MainWindow", "\350\257\267\350\276\223\345\205\245\345\215\201\345\205\255\350\277\233\345\210\266\346\225\260\346\215\256\357\274\214\344\276\213\345\246\202\357\274\23201 03 00 00 00 01", nullptr));
        pushButtonSendB->setText(QCoreApplication::translate("MainWindow", "\345\217\221\351\200\201", nullptr));
        groupBoxDisplayB->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243B\346\225\260\346\215\256\346\230\276\347\244\272", nullptr));
        groupBoxDisplayB->setStyleSheet(QCoreApplication::translate("MainWindow", "QGroupBox {\n"
"    font-weight: bold;\n"
"    border: 2px solid #4CAF50;\n"
"    border-radius: 8px;\n"
"    margin-top: 1ex;\n"
"    padding-top: 10px;\n"
"    background-color: #f8f9fa;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 8px 0 8px;\n"
"    color: #4CAF50;\n"
"}", nullptr));
        textBrowserB->setStyleSheet(QCoreApplication::translate("MainWindow", "QTextBrowser {\n"
"    border: 2px solid #e0e0e0;\n"
"    border-radius: 6px;\n"
"    background-color: #fafafa;\n"
"    font-family: 'Consolas', 'Monaco', monospace;\n"
"    font-size: 10pt;\n"
"    padding: 8px;\n"
"}\n"
"QTextBrowser:focus {\n"
"    border-color: #2196F3;\n"
"}", nullptr));
        mainToolBar->setWindowTitle(QCoreApplication::translate("MainWindow", "\345\267\245\345\205\267\346\240\217", nullptr));
        menuFile->setTitle(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266", nullptr));
        menuTest->setTitle(QCoreApplication::translate("MainWindow", "\346\243\200\346\265\213", nullptr));
        menuConfig->setTitle(QCoreApplication::translate("MainWindow", "\351\205\215\347\275\256", nullptr));
        menuReport->setTitle(QCoreApplication::translate("MainWindow", "\346\212\245\350\241\250", nullptr));
        menuTools->setTitle(QCoreApplication::translate("MainWindow", "\345\267\245\345\205\267", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
