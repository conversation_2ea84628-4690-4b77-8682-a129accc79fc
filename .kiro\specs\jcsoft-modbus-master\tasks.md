# JcSoft Modbus Master 实现计划

- [ ] 1. 建立项目基础架构和核心接口
  - 创建项目目录结构和基础类定义
  - 实现核心接口和抽象基类
  - 设置Qt项目配置和依赖管理
  - _需求: 1.1, 2.1_

- [ ] 1.1 创建核心基类和接口定义
  - 实现ModbusSerialBase抽象基类，定义通信接口
  - 创建SerialManager类框架，定义串口管理接口
  - 建立CommunicationState和TestExecutionState数据结构
  - 编写基础异常类CommunicationException、BusinessLogicException
  - _需求: 1.1, 2.1_

- [ ] 1.2 实现CRC校验工具类
  - 编写CrcUtils类实现CRC16和CRC32算法
  - 创建calculateCRC16和calculateCRC16Bytes方法
  - 编写CRC校验的单元测试用例
  - 验证CRC计算结果与标准Modbus规范一致
  - _需求: 2.1, 2.2_

- [ ] 2. 实现串口通信核心功能
  - 开发ModbusSerialBase通信基类
  - 实现SerialManager双串口管理
  - 创建异步数据收发机制
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 2.1 实现ModbusSerialBase通信基类
  - 编写connectDevice方法实现串口连接逻辑
  - 实现sendRawData方法处理数据发送
  - 创建processReceivedData方法处理接收数据缓冲
  - 添加byteArrayToHex工具方法进行数据格式转换
  - 实现连接状态管理和错误处理机制
  - _需求: 1.1, 1.3, 1.4_

- [ ] 2.2 开发ModbusSerialA和ModbusSerialB具体实现
  - 继承ModbusSerialBase创建具体串口类
  - 实现各自的特定配置和行为差异
  - 添加串口标识和日志区分功能
  - 编写串口通信的集成测试用例
  - _需求: 1.1, 1.2_

- [ ] 2.3 实现SerialManager统一管理类
  - 编写connectPort方法统一管理双串口连接
  - 实现getAvailablePorts方法扫描可用串口
  - 创建sendData方法路由数据到指定串口
  - 添加连接状态监控和错误事件处理
  - 实现信号槽机制传递通信事件
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 3. 开发Modbus协议处理功能
  - 实现Modbus RTU帧构建和解析
  - 创建命令生成和响应处理机制
  - 添加协议验证和错误检测
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5_

- [ ] 3.1 实现Modbus帧构建功能
  - 创建buildModbusFrame方法构建标准RTU帧
  - 实现站号、功能码、数据域的组装逻辑
  - 添加CRC16校验码自动计算和附加
  - 编写不同功能码的帧构建方法（读线圈、读寄存器、写单个、写多个）
  - _需求: 2.1, 2.2_

- [ ] 3.2 实现Modbus响应解析功能
  - 创建parseModbusResponse方法解析响应帧
  - 实现CRC校验验证和数据完整性检查
  - 添加异常响应处理和错误码解析
  - 编写数据提取和格式转换功能
  - _需求: 2.2, 2.3_

- [ ] 3.3 添加协议超时和重试机制
  - 实现QTimer基础的超时检测机制
  - 创建重试逻辑和次数限制功能
  - 添加超时事件处理和用户通知
  - 编写协议层的错误恢复策略
  - _需求: 2.3, 2.4_

- [ ] 4. 创建主窗口和基础UI框架
  - 实现MainWindow主界面布局
  - 创建串口配置和控制面板
  - 添加数据显示和日志记录区域
  - _需求: 1.1, 1.2, 1.3, 7.1, 7.2, 7.4, 9.1, 9.2_

- [ ] 4.1 实现MainWindow主界面框架
  - 创建主窗口布局包含菜单栏、工具栏、状态栏
  - 设计左右分栏布局：串口配置区和数据显示区
  - 添加串口参数配置控件（端口、波特率、校验位等）
  - 实现连接/断开按钮和状态指示器
  - _需求: 1.1, 1.2, 9.1_

- [ ] 4.2 创建数据收发显示区域
  - 实现双串口数据显示的文本浏览器
  - 添加发送数据输入框和发送按钮
  - 创建数据格式化显示（十六进制、时间戳）
  - 实现发送和接收数据的颜色区分显示
  - 添加清空日志功能按钮
  - _需求: 2.5, 7.1, 7.2, 7.4_

- [ ] 4.3 实现串口管理UI交互逻辑
  - 连接串口配置控件与SerialManager
  - 实现串口连接状态的UI更新逻辑
  - 添加串口列表刷新和自动检测功能
  - 创建错误提示和状态栏消息显示
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 9.2, 9.3_

- [ ] 5. 开发测试配置管理功能
  - 实现MappingWindow测试配置界面
  - 创建设备映射关系管理
  - 添加测试步骤生成和编辑功能
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5_

- [ ] 5.1 创建MappingWindow基础界面
  - 设计测试配置窗口布局和控件
  - 实现串口和设备选择下拉框
  - 创建映射关系列表显示控件
  - 添加添加、编辑、删除映射的按钮
  - 实现撤销/重做功能的UI控件
  - _需求: 3.1, 3.4_

- [ ] 5.2 实现映射关系数据管理
  - 创建MappingRelation数据结构和管理逻辑
  - 实现addMapping方法添加串口设备映射
  - 编写updateMappingList方法更新显示列表
  - 添加映射关系验证（COMA对应控制器，COMB对应保护装置）
  - 实现撤销/重做栈管理映射操作历史
  - _需求: 3.1, 3.4_

- [ ] 5.3 实现测试步骤自动生成功能
  - 创建generateTestSteps方法根据映射生成测试步骤
  - 实现控制器和保护装置的命令映射表
  - 编写TestStep数据结构和管理逻辑
  - 添加测试步骤的编辑和验证功能
  - _需求: 3.3, 3.4_

- [ ] 6. 实现流程图可视化功能
  - 创建QGraphicsScene流程图显示
  - 实现测试步骤的图形化表示
  - 添加流程图交互和编辑功能
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 6.1 创建流程图基础绘制功能
  - 实现updateGraphicalMapping方法创建流程图
  - 设计蛇形布局算法排列测试步骤节点
  - 创建节点和连接线的绘制逻辑
  - 添加开始和结束节点的特殊样式
  - _需求: 5.1_

- [ ] 6.2 实现流程图状态显示和交互
  - 添加测试步骤执行状态的视觉反馈（颜色、图标）
  - 实现节点高亮显示当前执行步骤
  - 创建双击节点编辑测试步骤的功能
  - 添加右键菜单提供节点操作选项
  - 实现流程图的缩放和平移功能
  - _需求: 5.2, 5.3, 5.4, 5.5_

- [ ] 6.3 添加流程图工具提示和详情显示
  - 实现节点悬停显示测试步骤详细信息
  - 创建测试结果的图形化状态指示
  - 添加流程图导出和打印功能
  - 实现流程图显示模式切换（仅流程图/详细信息）
  - _需求: 5.2, 5.3_

- [ ] 7. 开发自动化测试执行引擎
  - 实现测试序列执行控制器
  - 创建测试结果比较和验证机制
  - 添加测试进度监控和报告功能
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 7.1 实现测试执行控制器
  - 创建startTest方法启动自动化测试序列
  - 实现runNextTest方法按顺序执行测试步骤
  - 添加测试暂停、停止和重置功能
  - 编写测试执行状态管理和进度跟踪
  - _需求: 4.1, 4.2_

- [ ] 7.2 创建测试结果验证机制
  - 实现期望结果与实际结果的比较逻辑
  - 添加多种比较模式（精确匹配、模式匹配、范围匹配）
  - 创建测试通过/失败的判定规则
  - 编写测试结果记录和统计功能
  - _需求: 4.3, 4.4_

- [ ] 7.3 添加测试超时和错误处理
  - 实现测试步骤超时检测和处理
  - 创建测试失败时的错误记录和继续执行逻辑
  - 添加测试异常情况的恢复机制
  - 编写测试执行日志和调试信息记录
  - _需求: 4.4, 4.5_

- [ ] 8. 实现定值召唤功能模块
  - 创建SetValueDialog定值管理界面
  - 实现设备定值读取和写入功能
  - 添加定值配置文件管理
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [ ] 8.1 创建SetValueDialog基础界面
  - 设计定值召唤窗口布局和控件
  - 实现设备型号选择和串口配置
  - 创建定值参数表格显示控件
  - 添加读取、写入、清空等操作按钮
  - _需求: 6.1_

- [ ] 8.2 实现ParseItem数据结构和管理
  - 创建ParseItem结构体定义定值参数
  - 实现定值参数的增删改查功能
  - 编写定值数据的验证和格式化逻辑
  - 添加定值范围检查和输入校验
  - _需求: 6.2, 6.3_

- [ ] 8.3 开发定值读写通信功能
  - 实现Modbus读取寄存器命令构建
  - 创建定值数据解析和显示逻辑
  - 编写定值写入命令生成和执行
  - 添加定值一致性检查和批量写入功能
  - _需求: 6.2, 6.4, 6.5_

- [ ] 9. 实现配置文件管理系统
  - 创建统一的配置文件管理机制
  - 实现多格式配置文件的导入导出
  - 添加配置文件路径管理和验证
  - _需求: 3.2, 6.1, 8.1, 8.2, 8.3, 8.4, 8.5_

- [ ] 9.1 实现配置文件路径管理
  - 创建getDevDocPath和getConfigFilePath方法
  - 实现ensureDevDocExists方法检查目录存在性
  - 添加配置文件路径的统一管理和验证
  - 编写配置文件访问权限检查功能
  - _需求: 8.1, 8.2_

- [ ] 9.2 开发多格式配置文件支持
  - 实现JSON格式配置文件的读写功能
  - 添加XML格式配置文件的解析和生成
  - 创建CSV格式定值文件的处理逻辑
  - 编写配置文件格式验证和转换功能
  - _需求: 3.2, 8.1, 8.2_

- [ ] 9.3 实现配置文件导入导出功能
  - 创建importConfig和exportConfig方法
  - 实现配置文件的备份和恢复机制
  - 添加配置文件版本管理和兼容性检查
  - 编写配置文件合并和冲突解决逻辑
  - _需求: 3.2, 8.3, 8.4_

- [ ] 10. 开发日志记录和数据管理
  - 实现统一的日志记录系统
  - 创建通信数据的格式化显示
  - 添加日志过滤和搜索功能
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 10.1 实现统一日志记录系统
  - 创建logMessage方法统一日志记录接口
  - 实现不同日志级别的分类和显示
  - 添加时间戳和来源标识的日志格式
  - 编写日志文件的自动保存和轮转功能
  - _需求: 7.1, 7.2, 7.3_

- [ ] 10.2 创建通信数据格式化显示
  - 实现十六进制数据的格式化显示
  - 添加发送和接收数据的颜色区分
  - 创建数据包的结构化解析和显示
  - 编写数据统计和分析功能
  - _需求: 7.1, 7.2, 7.5_

- [ ] 10.3 添加日志管理和导出功能
  - 实现日志清空和历史记录管理
  - 创建日志过滤和搜索功能
  - 添加日志导出到文件的功能
  - 编写日志数据的压缩和归档机制
  - _需求: 7.4, 7.5_

- [ ] 11. 实现报告生成和导出功能
  - 创建测试报告生成引擎
  - 实现HTML和PDF格式报告导出
  - 添加报告模板和自定义功能
  - _需求: 10.1, 10.2, 10.3, 10.4, 10.5_

- [ ] 11.1 创建测试报告生成引擎
  - 实现generateTestReport方法生成报告内容
  - 创建测试结果统计和分析逻辑
  - 添加测试数据的图表和可视化功能
  - 编写报告模板和格式化功能
  - _需求: 10.1, 10.2_

- [ ] 11.2 实现HTML报告生成和导出
  - 创建HTML报告模板和样式定义
  - 实现测试数据到HTML的转换逻辑
  - 添加交互式图表和数据展示功能
  - 编写HTML报告的浏览器预览功能
  - _需求: 10.1, 10.4_

- [ ] 11.3 添加PDF报告导出功能
  - 实现HTML到PDF的转换功能
  - 创建PDF报告的页面布局和格式控制
  - 添加PDF报告的打印和分享功能
  - 编写报告文件的命名和保存逻辑
  - _需求: 10.3, 10.5_

- [ ] 12. 实现用户界面优化和交互增强
  - 优化界面布局和响应性设计
  - 添加用户体验增强功能
  - 实现界面主题和个性化设置
  - _需求: 9.1, 9.2, 9.3, 9.4, 9.5_

- [ ] 12.1 优化界面布局和响应性
  - 实现窗口大小调整时的布局自适应
  - 优化控件间距和对齐方式
  - 添加界面元素的合理分组和标签
  - 编写界面状态的保存和恢复功能
  - _需求: 9.1, 9.4_

- [ ] 12.2 增强用户交互体验
  - 实现操作的即时反馈和状态提示
  - 添加快捷键和右键菜单功能
  - 创建拖拽操作和批量处理功能
  - 编写用户操作的撤销和重做机制
  - _需求: 9.2, 9.5_

- [ ] 12.3 实现错误处理和用户提示
  - 创建友好的错误消息和解决建议
  - 实现操作确认和风险提示对话框
  - 添加帮助文档和操作指南功能
  - 编写用户反馈和问题报告机制
  - _需求: 9.3_

- [ ] 13. 系统集成测试和性能优化
  - 执行完整的系统集成测试
  - 进行性能分析和优化
  - 实现系统稳定性和可靠性测试
  - _需求: 所有需求的综合验证_

- [ ] 13.1 执行系统功能集成测试
  - 测试串口通信的完整流程
  - 验证Modbus协议的正确实现
  - 测试测试配置和自动化执行功能
  - 验证定值召唤的读写操作
  - 测试配置文件的导入导出功能
  - _需求: 1.1-1.5, 2.1-2.5, 3.1-3.5, 4.1-4.5, 6.1-6.5_

- [ ] 13.2 进行性能分析和优化
  - 分析通信延迟和响应时间
  - 优化内存使用和资源管理
  - 测试大数据量处理的性能
  - 优化界面渲染和用户体验
  - _需求: 所有需求的性能要求_

- [ ] 13.3 实现系统稳定性测试
  - 执行长时间运行稳定性测试
  - 测试异常情况下的系统恢复能力
  - 验证多设备并发通信的稳定性
  - 测试系统在不同环境下的兼容性
  - _需求: 所有需求的稳定性要求_