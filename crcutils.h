#ifndef CRCUTILS_H
#define CRCUTILS_H

#include <QByteArray>

class CrcUtils
{
public:
    CrcUtils();
    
    // 计算CRC16校验码，返回quint16类型的CRC值
    static quint16 calculateCRC16(const QByteArray &data);
    
    // 计算CRC16校验码并返回字节数组（低字节在前，高字节在后）
    static QByteArray calculateCRC16Bytes(const QByteArray &data);
    
    // 计算CRC32校验码
    static QByteArray calculateCRC32(const QByteArray &data);
};

#endif // CRCUTILS_H