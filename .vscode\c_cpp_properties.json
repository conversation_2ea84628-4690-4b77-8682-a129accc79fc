{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "D:/Qt/6.5.3/mingw_64/include/**", "D:/Qt/6.8.2/mingw_64/include/**", "D:/Qt/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/**", "D:/Qt/Tools/mingw1120_64/x86_64-w64-mingw32/include/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "windowsSdkVersion": "10.0.22000.0", "compilerPath": "D:/Qt/Tools/mingw1120_64/bin/g++.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-gcc-x64", "compileCommands": ["${workspaceFolder}/.qtc_clangd/compile_commands.json"]}], "version": 4}