/****************************************************************************
** Meta object code from reading C++ file 'setvaluedialog.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../setvaluedialog.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'setvaluedialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN14SetValueDialogE_t {};
} // unnamed namespace


#ifdef QT_MOC_HAS_STRINGDATA
static constexpr auto qt_meta_stringdata_ZN14SetValueDialogE = QtMocHelpers::stringData(
    "SetValueDialog",
    "on_comboBoxPort_currentIndexChanged",
    "",
    "index",
    "on_comboBoxDeviceModel_currentIndexChanged",
    "on_pushButtonSend_clicked",
    "on_pushButtonSelectValueFile_clicked",
    "on_checkBoxCRC16_stateChanged",
    "state",
    "on_tableWidgetParse_itemDoubleClicked",
    "QTableWidgetItem*",
    "item",
    "on_pushButtonGenerateReport_clicked",
    "on_pushButtonWriteValue_clicked",
    "on_pushButtonClearLog_clicked",
    "showContextMenu",
    "pos",
    "addParseItem",
    "editParseItem",
    "deleteParseItem",
    "deleteSelectedParseItems",
    "saveParseItems",
    "loadParseItems",
    "handleSerialMessageReceived",
    "message",
    "onTableItemChanged",
    "performDelayedSave"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA

Q_CONSTINIT static const uint qt_meta_data_ZN14SetValueDialogE[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
      19,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,  128,    2, 0x08,    1 /* Private */,
       4,    1,  131,    2, 0x08,    3 /* Private */,
       5,    0,  134,    2, 0x08,    5 /* Private */,
       6,    0,  135,    2, 0x08,    6 /* Private */,
       7,    1,  136,    2, 0x08,    7 /* Private */,
       9,    1,  139,    2, 0x08,    9 /* Private */,
      12,    0,  142,    2, 0x08,   11 /* Private */,
      13,    0,  143,    2, 0x08,   12 /* Private */,
      14,    0,  144,    2, 0x08,   13 /* Private */,
      15,    1,  145,    2, 0x08,   14 /* Private */,
      17,    0,  148,    2, 0x08,   16 /* Private */,
      18,    0,  149,    2, 0x08,   17 /* Private */,
      19,    0,  150,    2, 0x08,   18 /* Private */,
      20,    0,  151,    2, 0x08,   19 /* Private */,
      21,    0,  152,    2, 0x08,   20 /* Private */,
      22,    0,  153,    2, 0x08,   21 /* Private */,
      23,    1,  154,    2, 0x08,   22 /* Private */,
      25,    1,  157,    2, 0x08,   24 /* Private */,
      26,    0,  160,    2, 0x08,   26 /* Private */,

 // slots: parameters
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    8,
    QMetaType::Void, 0x80000000 | 10,   11,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPoint,   16,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   24,
    QMetaType::Void, 0x80000000 | 10,   11,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject SetValueDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_ZN14SetValueDialogE.offsetsAndSizes,
    qt_meta_data_ZN14SetValueDialogE,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_tag_ZN14SetValueDialogE_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<SetValueDialog, std::true_type>,
        // method 'on_comboBoxPort_currentIndexChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'on_comboBoxDeviceModel_currentIndexChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'on_pushButtonSend_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonSelectValueFile_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_checkBoxCRC16_stateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'on_tableWidgetParse_itemDoubleClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QTableWidgetItem *, std::false_type>,
        // method 'on_pushButtonGenerateReport_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonWriteValue_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonClearLog_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'showContextMenu'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>,
        // method 'addParseItem'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'editParseItem'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'deleteParseItem'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'deleteSelectedParseItems'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'saveParseItems'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'loadParseItems'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'handleSerialMessageReceived'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onTableItemChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QTableWidgetItem *, std::false_type>,
        // method 'performDelayedSave'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void SetValueDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<SetValueDialog *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->on_comboBoxPort_currentIndexChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 1: _t->on_comboBoxDeviceModel_currentIndexChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 2: _t->on_pushButtonSend_clicked(); break;
        case 3: _t->on_pushButtonSelectValueFile_clicked(); break;
        case 4: _t->on_checkBoxCRC16_stateChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 5: _t->on_tableWidgetParse_itemDoubleClicked((*reinterpret_cast< std::add_pointer_t<QTableWidgetItem*>>(_a[1]))); break;
        case 6: _t->on_pushButtonGenerateReport_clicked(); break;
        case 7: _t->on_pushButtonWriteValue_clicked(); break;
        case 8: _t->on_pushButtonClearLog_clicked(); break;
        case 9: _t->showContextMenu((*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[1]))); break;
        case 10: _t->addParseItem(); break;
        case 11: _t->editParseItem(); break;
        case 12: _t->deleteParseItem(); break;
        case 13: _t->deleteSelectedParseItems(); break;
        case 14: _t->saveParseItems(); break;
        case 15: _t->loadParseItems(); break;
        case 16: _t->handleSerialMessageReceived((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 17: _t->onTableItemChanged((*reinterpret_cast< std::add_pointer_t<QTableWidgetItem*>>(_a[1]))); break;
        case 18: _t->performDelayedSave(); break;
        default: ;
        }
    }
}

const QMetaObject *SetValueDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SetValueDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ZN14SetValueDialogE.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int SetValueDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 19)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 19;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 19)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 19;
    }
    return _id;
}
QT_WARNING_POP
