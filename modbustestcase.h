#ifndef MODBUSTESTCASE_H
#define MODBUSTESTCASE_H

#include <QString>

// Modbus测试用例结构体
struct ModbusTestCase {
    QString description;
    int stationId;
    int functionCode;
    QString sendData; // 旧格式，兼容性保留
    QString expectedResponse; // 旧格式，兼容性保留
    QString actualResponse; // 旧格式，实际响应字段，可能需要映射或移除
    int timeout;
    QString crcType; // 旧格式，由 crc16 替代
    QString result;
    QString targetSerialPort; // A 或 B

    // 新统一格式字段
    QString actualReceiveA;
    QString actualReceiveB;
    QString expectReceiveA;
    QString expectReceiveB;
    QString sentDataA;
    QString sentDataB;
    QString crc16;
};

#endif // MODBUSTESTCASE_H