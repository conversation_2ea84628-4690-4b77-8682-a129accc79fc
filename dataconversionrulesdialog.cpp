#include "dataconversionrulesdialog.h"
#include <QDebug>
#include <QRegularExpression>

DataConversionRulesDialog::DataConversionRulesDialog(QWidget *parent)
    : QDialog(parent)
{
    setWindowTitle("数据转换规则配置");
    setModal(true);
    resize(800, 600);
    
    setupUI();
    
    // 加载现有规则或创建默认规则
    rules = loadRulesFromFile();
    if (rules.isEmpty()) {
        createDefaultRules();
    }
    updateTable();
}

DataConversionRulesDialog::~DataConversionRulesDialog()
{
}

void DataConversionRulesDialog::setupUI()
{
    mainLayout = new QVBoxLayout(this);
    
    // 说明标签
    QLabel *infoLabel = new QLabel("配置期望数据（定值数据读取）到期望数据的转换规则：");
    infoLabel->setStyleSheet("font-weight: bold; color: #2c3e50; margin: 10px 0;");
    mainLayout->addWidget(infoLabel);
    
    // 规则表格
    rulesTable = new QTableWidget();
    rulesTable->setColumnCount(5);
    QStringList headers;
    headers << "源数据模式" << "目标值" << "特性选择" << "投退标志" << "描述";
    rulesTable->setHorizontalHeaderLabels(headers);
    rulesTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    rulesTable->setAlternatingRowColors(true);
    rulesTable->horizontalHeader()->setStretchLastSection(true);
    rulesTable->setColumnWidth(0, 150);
    rulesTable->setColumnWidth(1, 80);
    rulesTable->setColumnWidth(2, 80);
    rulesTable->setColumnWidth(3, 80);
    
    connect(rulesTable, &QTableWidget::itemSelectionChanged, this, &DataConversionRulesDialog::onTableSelectionChanged);
    connect(rulesTable, &QTableWidget::itemDoubleClicked, this, &DataConversionRulesDialog::onEditRuleClicked);
    
    mainLayout->addWidget(rulesTable);
    
    // 按钮布局
    buttonLayout = new QHBoxLayout();
    
    addButton = new QPushButton("添加规则");
    editButton = new QPushButton("编辑规则");
    deleteButton = new QPushButton("删除规则");
    resetButton = new QPushButton("恢复默认");
    
    editButton->setEnabled(false);
    deleteButton->setEnabled(false);
    
    connect(addButton, &QPushButton::clicked, this, &DataConversionRulesDialog::onAddRuleClicked);
    connect(editButton, &QPushButton::clicked, this, &DataConversionRulesDialog::onEditRuleClicked);
    connect(deleteButton, &QPushButton::clicked, this, &DataConversionRulesDialog::onDeleteRuleClicked);
    connect(resetButton, &QPushButton::clicked, this, &DataConversionRulesDialog::onResetToDefaultClicked);
    
    buttonLayout->addWidget(addButton);
    buttonLayout->addWidget(editButton);
    buttonLayout->addWidget(deleteButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(resetButton);
    
    mainLayout->addLayout(buttonLayout);
    
    // 对话框按钮
    buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    connect(buttonBox, &QDialogButtonBox::accepted, this, &DataConversionRulesDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, this, &DataConversionRulesDialog::reject);
    
    mainLayout->addWidget(buttonBox);
}

void DataConversionRulesDialog::updateTable()
{
    rulesTable->setRowCount(rules.size());
    
    for (int i = 0; i < rules.size(); ++i) {
        const DataConversionRule &rule = rules[i];
        
        // 源数据模式
        QTableWidgetItem *patternItem = new QTableWidgetItem(rule.sourcePattern);
        patternItem->setFlags(patternItem->flags() & ~Qt::ItemIsEditable);
        rulesTable->setItem(i, 0, patternItem);
        
        // 目标值
        QTableWidgetItem *valueItem = new QTableWidgetItem(rule.targetValue);
        valueItem->setFlags(valueItem->flags() & ~Qt::ItemIsEditable);
        rulesTable->setItem(i, 1, valueItem);
        
        // 特性选择
        QTableWidgetItem *featureItem = new QTableWidgetItem(rule.featureSelection ? "✓" : "×");
        featureItem->setFlags(featureItem->flags() & ~Qt::ItemIsEditable);
        featureItem->setTextAlignment(Qt::AlignCenter);
        rulesTable->setItem(i, 2, featureItem);
        
        // 投退标志
        QTableWidgetItem *protectionItem = new QTableWidgetItem(rule.enableProtection ? "✓" : "×");
        protectionItem->setFlags(protectionItem->flags() & ~Qt::ItemIsEditable);
        protectionItem->setTextAlignment(Qt::AlignCenter);
        rulesTable->setItem(i, 3, protectionItem);
        
        // 描述
        QTableWidgetItem *descItem = new QTableWidgetItem(rule.description);
        descItem->setFlags(descItem->flags() & ~Qt::ItemIsEditable);
        rulesTable->setItem(i, 4, descItem);
    }
}

void DataConversionRulesDialog::createDefaultRules()
{
    rules.clear();
    
    // 创建默认规则
    DataConversionRule rule;
    
    // DEF TIME -> 1, 特性选择=true
    rule.sourcePattern = "DEF TIME";
    rule.targetValue = "1";
    rule.featureSelection = true;
    rule.enableProtection = false;
    rule.description = "定时特性，期望值为1，特性选择选中";
    rule.isNumberPattern = false;
    rules.append(rule);
    
    // INV TIME -> 0, 特性选择=true
    rule.sourcePattern = "INV TIME";
    rule.targetValue = "0";
    rule.featureSelection = true;
    rule.enableProtection = false;
    rule.description = "反时限特性，期望值为0，特性选择选中";
    rule.isNumberPattern = false;
    rules.append(rule);
    
    // TRIP -> 1, 投退标志=true
    rule.sourcePattern = "TRIP";
    rule.targetValue = "1";
    rule.featureSelection = false;
    rule.enableProtection = true;
    rule.description = "跳闸，期望值为1，投退标志选中";
    rule.isNumberPattern = false;
    rules.append(rule);
    
    // OFF -> 0, 投退标志=false
    rule.sourcePattern = "OFF";
    rule.targetValue = "0";
    rule.featureSelection = false;
    rule.enableProtection = false;
    rule.description = "关闭，期望值为0，投退标志不选中";
    rule.isNumberPattern = false;
    rules.append(rule);
    
    // ALARM -> 2, 投退标志=true
    rule.sourcePattern = "ALARM";
    rule.targetValue = "2";
    rule.featureSelection = false;
    rule.enableProtection = true;
    rule.description = "告警，期望值为2，投退标志选中";
    rule.isNumberPattern = false;
    rules.append(rule);
    
    // 数字+字母模式 -> 提取数字
    rule.sourcePattern = "数字+字母";
    rule.targetValue = "提取数字";
    rule.featureSelection = false;
    rule.enableProtection = false;
    rule.description = "数字+字母形式，提取数字部分作为期望值";
    rule.isNumberPattern = true;
    rules.append(rule);
    
    // 空数据 -> 0（最低优先级）
    rule.sourcePattern = "空数据";
    rule.targetValue = "0";
    rule.featureSelection = false;
    rule.enableProtection = false;
    rule.description = "空数据转换为0（最低优先级处理）";
    rule.isNumberPattern = false;
    rules.append(rule);
}

void DataConversionRulesDialog::onAddRuleClicked()
{
    DataConversionRule newRule;
    RuleEditDialog dialog(newRule, this);
    
    if (dialog.exec() == QDialog::Accepted) {
        rules.append(dialog.getRule());
        updateTable();
    }
}

void DataConversionRulesDialog::onEditRuleClicked()
{
    int currentRow = rulesTable->currentRow();
    if (currentRow < 0 || currentRow >= rules.size()) {
        return;
    }
    
    RuleEditDialog dialog(rules[currentRow], this);
    
    if (dialog.exec() == QDialog::Accepted) {
        rules[currentRow] = dialog.getRule();
        updateTable();
    }
}

void DataConversionRulesDialog::onDeleteRuleClicked()
{
    int currentRow = rulesTable->currentRow();
    if (currentRow < 0 || currentRow >= rules.size()) {
        return;
    }
    
    int ret = QMessageBox::question(this, "确认删除", 
        QString("确定要删除规则 '%1' 吗？").arg(rules[currentRow].sourcePattern),
        QMessageBox::Yes | QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        rules.removeAt(currentRow);
        updateTable();
    }
}

void DataConversionRulesDialog::onResetToDefaultClicked()
{
    int ret = QMessageBox::question(this, "确认重置", 
        "确定要恢复到默认规则吗？这将删除所有自定义规则。",
        QMessageBox::Yes | QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        createDefaultRules();
        updateTable();
    }
}

void DataConversionRulesDialog::onTableSelectionChanged()
{
    int currentRow = rulesTable->currentRow();
    bool hasSelection = (currentRow >= 0);
    
    editButton->setEnabled(hasSelection);
    deleteButton->setEnabled(hasSelection);
}

void DataConversionRulesDialog::accept()
{
    // 保存规则到文件
    saveRulesToFile(rules);
    QDialog::accept();
}

void DataConversionRulesDialog::reject()
{
    QDialog::reject();
}

QList<DataConversionRule> DataConversionRulesDialog::getRules() const
{
    return rules;
}

void DataConversionRulesDialog::setRules(const QList<DataConversionRule> &newRules)
{
    rules = newRules;
    updateTable();
}

void DataConversionRulesDialog::applyRulesToData(const QString &sourceData, QString &targetValue, bool &featureSelection, bool &enableProtection, const QList<DataConversionRule> &rules, double minValue, const QString &displayValue, bool useDisplayValue)
{
    // 重置输出值
    targetValue.clear();
    featureSelection = false;
    enableProtection = false;
    
    QString trimmedSource = sourceData.trimmed();
    
    // 检查是否为纯数字（整数或小数）
    QRegularExpression pureNumberRegex("^-?\\d+(\\.\\d+)?$");
    if (pureNumberRegex.match(trimmedSource).hasMatch()) {
        targetValue = trimmedSource; // 直接转到期望数据栏
        qDebug() << "纯数字直接转换:" << trimmedSource << "->" << targetValue;
        return;
    }
    
    // 检查是否为小数+字母模式
    QRegularExpression decimalLetterRegex("^(-?\\d+\\.\\d+)[a-zA-Z]+$");
    QRegularExpressionMatch decimalLetterMatch = decimalLetterRegex.match(trimmedSource);
    if (decimalLetterMatch.hasMatch()) {
        targetValue = decimalLetterMatch.captured(1); // 提取小数部分
        qDebug() << "小数+字母规则:" << trimmedSource << "->" << targetValue;
        return;
    }
    
    // 遍历规则进行匹配
    for (const DataConversionRule &rule : rules) {
        if (rule.isNumberPattern) {
            // 数字+字母模式的特殊处理（整数+字母）
            QRegularExpression regex("^(\\d+)[a-zA-Z]+$");
            QRegularExpressionMatch match = regex.match(trimmedSource);
            if (match.hasMatch()) {
                targetValue = match.captured(1); // 提取数字部分
                featureSelection = rule.featureSelection;
                enableProtection = rule.enableProtection;
                qDebug() << "应用数字+字母规则:" << trimmedSource << "->" << targetValue;
                return;
            }
        } else {
            // 精确匹配
            if (trimmedSource.compare(rule.sourcePattern, Qt::CaseInsensitive) == 0) {
                targetValue = rule.targetValue;
                featureSelection = rule.featureSelection;
                enableProtection = rule.enableProtection;
                qDebug() << "应用规则:" << rule.sourcePattern << "->" << targetValue;
                return;
            }
        }
    }
    
    // 最后处理空数据：根据选项使用显示数据或最小值（最低优先级）
    if (trimmedSource.isEmpty()) {
        if (useDisplayValue && !displayValue.isEmpty()) {
            targetValue = displayValue;
            qDebug() << "空数据转换为显示数据:" << displayValue;
        } else {
            targetValue = QString::number(minValue);
            qDebug() << "空数据转换为最小值:" << minValue;
        }
        return;
    }
    
    qDebug() << "未找到匹配规则:" << trimmedSource;
}

QList<DataConversionRule> DataConversionRulesDialog::loadRulesFromFile()
{
    QList<DataConversionRule> loadedRules;
    
    QString filePath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/data_conversion_rules.json";
    QFile file(filePath);
    
    if (!file.exists()) {
        qDebug() << "规则文件不存在:" << filePath;
        return loadedRules;
    }
    
    if (!file.open(QIODevice::ReadOnly)) {
        qDebug() << "无法打开规则文件:" << filePath;
        return loadedRules;
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        qDebug() << "解析规则文件失败:" << error.errorString();
        return loadedRules;
    }
    
    QJsonArray rulesArray = doc.array();
    for (const QJsonValue &value : rulesArray) {
        QJsonObject ruleObj = value.toObject();
        
        DataConversionRule rule;
        rule.sourcePattern = ruleObj["sourcePattern"].toString();
        rule.targetValue = ruleObj["targetValue"].toString();
        rule.featureSelection = ruleObj["featureSelection"].toBool();
        rule.enableProtection = ruleObj["enableProtection"].toBool();
        rule.description = ruleObj["description"].toString();
        rule.isNumberPattern = ruleObj["isNumberPattern"].toBool();
        
        loadedRules.append(rule);
    }
    
    qDebug() << "从文件加载" << loadedRules.size() << "条规则";
    return loadedRules;
}

void DataConversionRulesDialog::saveRulesToFile(const QList<DataConversionRule> &rules)
{
    QString dirPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir dir;
    if (!dir.exists(dirPath)) {
        dir.mkpath(dirPath);
    }
    
    QString filePath = dirPath + "/data_conversion_rules.json";
    QFile file(filePath);
    
    if (!file.open(QIODevice::WriteOnly)) {
        qDebug() << "无法创建规则文件:" << filePath;
        return;
    }
    
    QJsonArray rulesArray;
    for (const DataConversionRule &rule : rules) {
        QJsonObject ruleObj;
        ruleObj["sourcePattern"] = rule.sourcePattern;
        ruleObj["targetValue"] = rule.targetValue;
        ruleObj["featureSelection"] = rule.featureSelection;
        ruleObj["enableProtection"] = rule.enableProtection;
        ruleObj["description"] = rule.description;
        ruleObj["isNumberPattern"] = rule.isNumberPattern;
        
        rulesArray.append(ruleObj);
    }
    
    QJsonDocument doc(rulesArray);
    file.write(doc.toJson());
    file.close();
    
    qDebug() << "保存" << rules.size() << "条规则到文件:" << filePath;
}

QString DataConversionRulesDialog::getRulesFilePath() const
{
    return QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/data_conversion_rules.json";
}

// RuleEditDialog 实现
RuleEditDialog::RuleEditDialog(const DataConversionRule &inputRule, QWidget *parent)
    : QDialog(parent), rule(inputRule)
{
    setWindowTitle("编辑转换规则");
    setModal(true);
    resize(400, 300);
    
    setupUI();
    updateUI();
}

RuleEditDialog::~RuleEditDialog()
{
}

void RuleEditDialog::setupUI()
{
    mainLayout = new QVBoxLayout(this);
    
    // 表单布局
    QGroupBox *formGroup = new QGroupBox("规则设置");
    formLayout = new QGridLayout(formGroup);
    
    // 源数据模式
    patternLabel = new QLabel("源数据模式:");
    patternEdit = new QLineEdit();
    patternEdit->setPlaceholderText("如: DEF TIME, INV TIME, TRIP, OFF, ALARM 等");
    connect(patternEdit, &QLineEdit::textChanged, this, &RuleEditDialog::onPatternChanged);
    formLayout->addWidget(patternLabel, 0, 0);
    formLayout->addWidget(patternEdit, 0, 1);
    
    // 目标值
    targetValueLabel = new QLabel("目标期望值:");
    targetValueEdit = new QLineEdit();
    targetValueEdit->setPlaceholderText("如: 1, 0, 2 等");
    formLayout->addWidget(targetValueLabel, 1, 0);
    formLayout->addWidget(targetValueEdit, 1, 1);
    
    // 特性选择
    featureSelectionCheck = new QCheckBox("特性选择");
    formLayout->addWidget(featureSelectionCheck, 2, 0, 1, 2);
    
    // 投退标志
    enableProtectionCheck = new QCheckBox("投退标志");
    formLayout->addWidget(enableProtectionCheck, 3, 0, 1, 2);
    
    // 数字+字母模式
    isNumberPatternCheck = new QCheckBox("数字+字母模式（自动提取数字部分）");
    connect(isNumberPatternCheck, &QCheckBox::toggled, this, &RuleEditDialog::onPatternChanged);
    formLayout->addWidget(isNumberPatternCheck, 4, 0, 1, 2);
    
    // 描述
    descriptionLabel = new QLabel("规则描述:");
    descriptionEdit = new QLineEdit();
    descriptionEdit->setPlaceholderText("规则的详细说明");
    formLayout->addWidget(descriptionLabel, 5, 0);
    formLayout->addWidget(descriptionEdit, 5, 1);
    
    mainLayout->addWidget(formGroup);
    
    // 对话框按钮
    buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    connect(buttonBox, &QDialogButtonBox::accepted, this, &RuleEditDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);
    
    mainLayout->addWidget(buttonBox);
}

void RuleEditDialog::updateUI()
{
    patternEdit->setText(rule.sourcePattern);
    targetValueEdit->setText(rule.targetValue);
    featureSelectionCheck->setChecked(rule.featureSelection);
    enableProtectionCheck->setChecked(rule.enableProtection);
    descriptionEdit->setText(rule.description);
    isNumberPatternCheck->setChecked(rule.isNumberPattern);
    
    onPatternChanged();
}

void RuleEditDialog::onPatternChanged()
{
    bool isNumberPattern = isNumberPatternCheck->isChecked();
    
    if (isNumberPattern) {
        patternEdit->setText("数字+字母");
        patternEdit->setEnabled(false);
        targetValueEdit->setText("提取数字");
        targetValueEdit->setEnabled(false);
    } else {
        patternEdit->setEnabled(true);
        targetValueEdit->setEnabled(true);
        if (patternEdit->text() == "数字+字母") {
            patternEdit->clear();
        }
        if (targetValueEdit->text() == "提取数字") {
            targetValueEdit->clear();
        }
    }
}

void RuleEditDialog::accept()
{
    QString pattern = patternEdit->text().trimmed();
    QString targetValue = targetValueEdit->text().trimmed();
    
    if (pattern.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入源数据模式！");
        return;
    }
    
    if (!isNumberPatternCheck->isChecked() && targetValue.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入目标期望值！");
        return;
    }
    
    rule.sourcePattern = pattern;
    rule.targetValue = targetValue;
    rule.featureSelection = featureSelectionCheck->isChecked();
    rule.enableProtection = enableProtectionCheck->isChecked();
    rule.description = descriptionEdit->text().trimmed();
    rule.isNumberPattern = isNumberPatternCheck->isChecked();
    
    QDialog::accept();
}

DataConversionRule RuleEditDialog::getRule() const
{
    return rule;
}