{"version": "0.2.0", "configurations": [{"name": "调试 JcSoft", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/release/JcSoft.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "C:/MinGW-w64/bin/gcc.exe", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "build"}]}