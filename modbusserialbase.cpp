#include "modbusserialbase.h"
#include <QDebug>
#include <QDateTime>

ModbusSerialBase::ModbusSerialBase(QObject *parent)
    : QObject(parent)
    , serialPort(new QSerialPort(this))
    , receiveTimer(new QTimer(this))
{
    // 设置接收超时定时器为单次触发模式
    receiveTimer->setSingleShot(true);
    
    // 当接收超时时处理数据
    connect(receiveTimer, &QTimer::timeout, this, &ModbusSerialBase::processReceivedData);
    
    // 连接串口数据接收信号
    connect(serialPort, &QSerialPort::readyRead, this, [this]() {
        QByteArray newData = serialPort->readAll();
        if (!newData.isEmpty()) {
            receiveBuffer.append(newData);
            receiveTimer->start(50); // 设置50ms超时，等待数据接收完整
        }
    });
}

ModbusSerialBase::~ModbusSerialBase()
{
    disconnectDevice();
}

bool ModbusSerialBase::connectDevice(const QString &portName, int baudRate,
                                   QSerialPort::Parity parity,
                                   QSerialPort::DataBits dataBits,
                                   QSerialPort::StopBits stopBits)
{
    if (!serialPort)
        return false;

    // 如果串口已经打开，先关闭
    if (serialPort->isOpen()) {
        serialPort->close();
    }

    // 配置串口参数
    serialPort->setPortName(portName);
    serialPort->setBaudRate(baudRate);
    serialPort->setParity(parity);
    serialPort->setDataBits(dataBits);
    serialPort->setStopBits(stopBits);
    serialPort->setFlowControl(QSerialPort::NoFlowControl);

    // 尝试打开串口
    if (!serialPort->open(QIODevice::ReadWrite)) {
        emit errorOccurred(tr("打开串口失败：") + serialPort->errorString());
        return false;
    }

    // 配置流控制信号
    serialPort->setDataTerminalReady(true);
    serialPort->setRequestToSend(true);

    emit connectionStateChanged(true);
    return true;
}

void ModbusSerialBase::disconnectDevice()
{
    if (serialPort && serialPort->isOpen()) {
        serialPort->close();
    }
    receiveBuffer.clear();
    emit connectionStateChanged(false);
}

bool ModbusSerialBase::isConnected() const
{
    return serialPort && serialPort->isOpen();
}

bool ModbusSerialBase::sendRawData(const QByteArray &data)
{
    if (!serialPort || !serialPort->isOpen()) {
        emit errorOccurred(tr("设备未连接"));
        return false;
    }

    // 发送原始数据
    qint64 bytesWritten = serialPort->write(data);
    if (bytesWritten != data.size()) {
        emit errorOccurred(tr("发送数据失败：") + serialPort->errorString());
        return false;
    }

    // 发送消息通知，添加 TX: 前缀
    QString hexData = byteArrayToHex(data);
    emit communicationMessageSent(QString("TX: %1").arg(hexData));
    
    // 确保数据立即发送
    serialPort->flush();
    return true;
}

void ModbusSerialBase::processReceivedData()
{
    if (receiveBuffer.isEmpty())
        return;

    // 处理接收到的数据，添加 RX: 前缀
    QString hexData = byteArrayToHex(receiveBuffer);
    emit communicationMessageReceived(QString("RX: %1").arg(hexData));
    
    // 清空接收缓冲区
    receiveBuffer.clear();
}

QString ModbusSerialBase::byteArrayToHex(const QByteArray &data)
{
    QString result;
    for (const char &byte : data) {
        result += QString("%1 ").arg(static_cast<quint8>(byte) & 0xFF, 2, 16, QChar('0')).toUpper();
    }
    return result.trimmed();
}
