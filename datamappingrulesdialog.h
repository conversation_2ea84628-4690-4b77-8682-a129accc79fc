#ifndef DATAMAPPINGRULESDIALOG_H
#define DATAMAPPINGRULESDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QFormLayout>
#include <QLabel>
#include <QComboBox>
#include <QListWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QHeaderView>
#include <QMessageBox>
#include <QGroupBox>
#include <QFileDialog>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QStandardPaths>
#include <QDir>
#include <QDateTime>
#include <QInputDialog>

// 数据映射规则结构体
struct DataMappingRule {
    QString excelColumn;        // Excel列序号（A, B, C...）
    QList<int> csvRows;        // CSV行号列表（支持1个excel列对应多个csv行）
    QString description;       // 描述信息
    
    DataMappingRule() {}
    DataMappingRule(const QString &col, const QList<int> &rows, const QString &desc = "")
        : excelColumn(col), csvRows(rows), description(desc) {}
    // 兼容旧版本的构造函数
    DataMappingRule(const QString &col, int row, const QString &desc = "")
        : excelColumn(col), description(desc) {
        if (row > 0) csvRows.append(row);
    }
};

// 数据映射配置结构体
struct DataMappingConfig {
    QString configName;         // 配置名称
    QString deviceModel;        // 设备型号
    QString excelFilePattern;   // Excel文件模式描述
    QString createTime;         // 创建时间
    QList<DataMappingRule> mappings;  // 映射规则列表
    
    DataMappingConfig() {}
};

class DataMappingRulesDialog : public QDialog
{
    Q_OBJECT

public:
    explicit DataMappingRulesDialog(const QString &deviceModel = "", const QStringList &excelHeaders = QStringList(), const QStringList &csvRowNames = QStringList(), QWidget *parent = nullptr);
    ~DataMappingRulesDialog();
    
    // 获取当前映射规则
    QList<DataMappingRule> getMappingRules() const;
    
    // 设置映射规则
    void setMappingRules(const QList<DataMappingRule> &rules);
    
    // 获取当前配置
    DataMappingConfig getCurrentConfig() const;
    
    // 静态方法：加载配置文件
    static QList<DataMappingConfig> loadAllConfigs();
    static DataMappingConfig loadConfig(const QString &configName);
    static bool saveConfig(const DataMappingConfig &config);
    static bool deleteConfig(const QString &configName);
    static QString getConfigDir();

private slots:
    void onConfigSelectionChanged();
    void onNewConfigClicked();
    void onSaveConfigClicked();
    void onSaveAsConfigClicked();
    void onDeleteConfigClicked();
    void onAddMappingClicked();
    void onEditMappingClicked();
    void onDeleteMappingClicked();
    void onTableSelectionChanged();
    void onResetClicked();

private:
    // UI组件
    QVBoxLayout *mainLayout;
    
    // 配置管理区域
    QGroupBox *configGroup;
    QHBoxLayout *configLayout;
    QLabel *configLabel;
    QComboBox *configComboBox;
    QPushButton *newConfigButton;
    QPushButton *saveConfigButton;
    QPushButton *saveAsConfigButton;
    QPushButton *deleteConfigButton;
    
    // 映射配置区域
    QGroupBox *mappingGroup;
    QVBoxLayout *mappingLayout;
    QHBoxLayout *mappingButtonLayout;
    QTableWidget *mappingTable;
    QPushButton *addMappingButton;
    QPushButton *editMappingButton;
    QPushButton *deleteMappingButton;
    QPushButton *resetButton;
    
    // 底部按钮
    QHBoxLayout *buttonLayout;
    QPushButton *okButton;
    QPushButton *cancelButton;
    
    // 数据成员
    QString currentDeviceModel;
    DataMappingConfig currentConfig;
    QList<DataMappingConfig> allConfigs;
    bool configModified;
    QStringList excelHeaders;
    QStringList csvRowNames;
    
    // 私有方法
    void setupUI();
    void loadConfigs();
    void updateConfigComboBox();
    void updateMappingTable();
    void updateButtonStates();
    bool saveCurrentConfig();
    bool validateConfig() const;
    QString generateConfigFileName(const QString &configName) const;
    void setConfigModified(bool modified);
};

// 映射规则编辑对话框
class MappingRuleEditDialog : public QDialog
{
    Q_OBJECT
    
public:
    explicit MappingRuleEditDialog(const DataMappingRule &rule, const QStringList &excelHeaders = QStringList(), const QStringList &csvRowNames = QStringList(), QWidget *parent = nullptr);
    ~MappingRuleEditDialog();
    
    DataMappingRule getRule() const;
    
private slots:
    void accept() override;
    void onSelectionChanged();
    
private:
    // UI组件
    QVBoxLayout *mainLayout;
    QFormLayout *formLayout;
    QLabel *excelColumnLabel;
    QComboBox *excelColumnComboBox;
    QLabel *csvRowLabel;
    QListWidget *csvRowListWidget;
    QLabel *descriptionLabel;
    QLineEdit *descriptionEdit;
    QHBoxLayout *buttonLayout;
    QPushButton *okButton;
    QPushButton *cancelButton;
    
    // 数据成员
    DataMappingRule rule;
    QStringList excelHeaders;
    QStringList csvRowNames;
    
    // 私有方法
    void setupUI();
    void updateUI();
    void populateComboBoxes();
};

#endif // DATAMAPPINGRULESDIALOG_H