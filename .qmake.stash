QMAKE_CXX.QT_COMPILER_STDCXX = 201703L
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 14
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 2
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 0
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    C:/MinGW-w64/include/c++/14.2.0 \
    C:/MinGW-w64/include/c++/14.2.0/x86_64-w64-mingw32 \
    C:/MinGW-w64/include/c++/14.2.0/backward \
    C:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include \
    C:/MinGW-w64/include \
    C:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed \
    C:/MinGW-w64/x86_64-w64-mingw32/include
QMAKE_CXX.LIBDIRS = \
    C:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0 \
    C:/MinGW-w64/lib/gcc \
    C:/MinGW-w64/x86_64-w64-mingw32/lib \
    C:/MinGW-w64/lib
