#include "crcutils.h"

CrcUtils::CrcUtils()
{
    // 构造函数，无需特殊初始化
}

// 计算CRC16校验码
quint16 CrcUtils::calculateCRC16(const QByteArray &data)
{
    quint16 crc = 0xFFFF;
    for (int i = 0; i < data.size(); ++i) {
        crc ^= static_cast<quint8>(data[i]);
        for (int j = 0; j < 8; ++j) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc = crc >> 1;
            }
        }
    }
    return crc; // 返回quint16类型的CRC值
}

// 计算CRC16校验码并返回字节数组（低字节在前，高字节在后）
QByteArray CrcUtils::calculateCRC16Bytes(const QByteArray &data)
{
    quint16 crc = calculateCRC16(data);
    QByteArray result;
    result.append(static_cast<char>(crc & 0xFF));        // 低字节
    result.append(static_cast<char>((crc >> 8) & 0xFF)); // 高字节
    return result;
}

// 计算CRC32校验码
QByteArray CrcUtils::calculateCRC32(const QByteArray &data)
{
    // 标准CRC32算法实现
    quint32 crc = 0xFFFFFFFF;
    for (int i = 0; i < data.size(); ++i) {
        crc ^= static_cast<quint8>(data[i]);
        for (int j = 0; j < 8; ++j) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0xEDB88320;
            } else {
                crc = crc >> 1;
            }
        }
    }
    crc = ~crc;
    
    QByteArray result;
    result.append(static_cast<char>(crc & 0xFF));         // 低字节
    result.append(static_cast<char>((crc >> 8) & 0xFF));
    result.append(static_cast<char>((crc >> 16) & 0xFF));
    result.append(static_cast<char>((crc >> 24) & 0xFF)); // 高字节
    return result;
}