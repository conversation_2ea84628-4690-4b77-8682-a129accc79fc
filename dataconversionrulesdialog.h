#ifndef DATACONVERSIONRULESDIALOG_H
#define DATACONVERSIONRULESDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QTableWidget>
#include <QHeaderView>
#include <QMessageBox>
#include <QCheckBox>
#include <QComboBox>
#include <QSpinBox>
#include <QGroupBox>
#include <QDialogButtonBox>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QStandardPaths>
#include <QDir>

// 数据转换规则结构体
struct DataConversionRule {
    QString sourcePattern;      // 源数据模式（如"DEF TIME", "INV TIME", "TRIP", "OFF", "ALARM", "数字+字母"）
    QString targetValue;        // 目标期望数据值（如"1", "0", "2"等）
    bool featureSelection;      // 特性选择复选框状态
    bool enableProtection;      // 投退标志复选框状态
    QString description;        // 规则描述
    bool isNumberPattern;       // 是否为数字+字母模式
    
    DataConversionRule() : featureSelection(false), enableProtection(false), isNumberPattern(false) {}
};

class DataConversionRulesDialog : public QDialog
{
    Q_OBJECT

public:
    explicit DataConversionRulesDialog(QWidget *parent = nullptr);
    ~DataConversionRulesDialog();
    
    // 获取所有规则
    QList<DataConversionRule> getRules() const;
    
    // 设置规则
    void setRules(const QList<DataConversionRule> &rules);
    
    // 应用规则到数据
    static void applyRulesToData(const QString &sourceData, QString &targetValue, bool &featureSelection, bool &enableProtection, const QList<DataConversionRule> &rules, double minValue = 0.0, const QString &displayValue = "", bool useDisplayValue = false);
    
    // 加载和保存规则
    static QList<DataConversionRule> loadRulesFromFile();
    static void saveRulesToFile(const QList<DataConversionRule> &rules);

private slots:
    void onAddRuleClicked();
    void onEditRuleClicked();
    void onDeleteRuleClicked();
    void onResetToDefaultClicked();
    void onTableSelectionChanged();
    void accept() override;
    void reject() override;

private:
    // UI组件
    QVBoxLayout *mainLayout;
    QHBoxLayout *buttonLayout;
    QTableWidget *rulesTable;
    QPushButton *addButton;
    QPushButton *editButton;
    QPushButton *deleteButton;
    QPushButton *resetButton;
    QDialogButtonBox *buttonBox;
    
    // 数据
    QList<DataConversionRule> rules;
    
    // 私有方法
    void setupUI();
    void updateTable();
    void createDefaultRules();
    QString getRulesFilePath() const;
};

// 规则编辑对话框
class RuleEditDialog : public QDialog
{
    Q_OBJECT
    
public:
    explicit RuleEditDialog(const DataConversionRule &rule, QWidget *parent = nullptr);
    ~RuleEditDialog();
    
    DataConversionRule getRule() const;
    
private slots:
    void onPatternChanged();
    void accept() override;
    
private:
    // UI组件
    QVBoxLayout *mainLayout;
    QGridLayout *formLayout;
    QLabel *patternLabel;
    QLineEdit *patternEdit;
    QLabel *targetValueLabel;
    QLineEdit *targetValueEdit;
    QCheckBox *featureSelectionCheck;
    QCheckBox *enableProtectionCheck;
    QLabel *descriptionLabel;
    QLineEdit *descriptionEdit;
    QCheckBox *isNumberPatternCheck;
    QDialogButtonBox *buttonBox;
    
    // 数据
    DataConversionRule rule;
    
    // 私有方法
    void setupUI();
    void updateUI();
};

#endif // DATACONVERSIONRULESDIALOG_H