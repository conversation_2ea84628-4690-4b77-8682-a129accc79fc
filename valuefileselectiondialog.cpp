#include "valuefileselectiondialog.h"
#include <QDebug>
#include <QFileInfo>
#include <QApplication>
#include <QRegularExpression>

ValueFileSelectionDialog::ValueFileSelectionDialog(const QString &excelFilePath,
                                                 const QString &deviceModel,
                                                 const QList<ParseItem> &parseItems,
                                                 QWidget *parent)
    : QDialog(parent)
    , excelFilePath(excelFilePath)
    , originalDeviceModel(deviceModel)
    , originalParseItems(parseItems)
    , updatedParseItems(parseItems)
    , enableDebugLog(false)  // 默认关闭调试日志
    , enableUILog(false)     // 默认关闭UI详细日志
    , cachedExcel(nullptr)
    , cachedWorkbook(nullptr)
{
    setWindowTitle("定值文件选择");
    setModal(false);  // 改为非模态对话框，允许操作其他界面
    resize(800, 600);
    
    // 设置窗口标志，添加最小化按钮
    setWindowFlags(Qt::Window | Qt::WindowMinimizeButtonHint | Qt::WindowCloseButtonHint);
    
    setupUI();
    loadConversionRules();
    loadMappingConfigs();
    initializeExcelData();  // 优化：一次性加载工作表名称和数据
}

ValueFileSelectionDialog::~ValueFileSelectionDialog()
{
    closeExcelFile();
}

void ValueFileSelectionDialog::setupUI()
{
    mainLayout = new QVBoxLayout(this);
    
    // 表单布局
    formLayout = new QFormLayout();
    
    // Excel文件路径显示
    labelExcelFile = new QLabel(QFileInfo(excelFilePath).fileName());
    labelExcelFile->setStyleSheet("font-weight: bold;");
    formLayout->addRow("Excel文件:", labelExcelFile);
    
    // 工作表选择
    labelWorksheet = new QLabel("工作表:");
    comboBoxWorksheet = new QComboBox();
    connect(comboBoxWorksheet, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &ValueFileSelectionDialog::onWorksheetChanged);
    formLayout->addRow(labelWorksheet, comboBoxWorksheet);
    
    // 设备型号选择
    labelDeviceModel = new QLabel("设备型号:");
    comboBoxDeviceModel = new QComboBox();
    comboBoxDeviceModel->addItems({"LL510P", "LM510P"});
    comboBoxDeviceModel->setCurrentText(originalDeviceModel);
    connect(comboBoxDeviceModel, &QComboBox::currentTextChanged, this, &ValueFileSelectionDialog::onDeviceModelChanged);
    formLayout->addRow(labelDeviceModel, comboBoxDeviceModel);
    
    // 对应规则选择
    labelMappingRule = new QLabel("对应规则:");
    comboBoxMappingRule = new QComboBox();
    comboBoxMappingRule->addItem("请选择对应规则");
    connect(comboBoxMappingRule, &QComboBox::currentTextChanged, this, &ValueFileSelectionDialog::onMappingRuleChanged);
    formLayout->addRow(labelMappingRule, comboBoxMappingRule);
    
    // 柜号输入
    labelCabinetNumber = new QLabel("柜号:");
    lineEditCabinetNumber = new QLineEdit();
    lineEditCabinetNumber->setPlaceholderText("请输入柜号（定值文件中的唯一标识）");
    connect(lineEditCabinetNumber, &QLineEdit::textChanged, this, &ValueFileSelectionDialog::onCabinetNumberChanged);
    formLayout->addRow(labelCabinetNumber, lineEditCabinetNumber);
    
    // 匹配选项
    labelMatchOptions = new QLabel("额外匹配方式:");
    QHBoxLayout *matchOptionsLayout = new QHBoxLayout();
    
    checkBoxExactMatch = new QCheckBox("精确匹配");
    checkBoxExactMatch->setToolTip("CSV字段名称与Excel列名称完全一致");
    checkBoxExactMatch->setChecked(false); // 默认不选中
    
    checkBoxPartialMatch = new QCheckBox("部分匹配");
    checkBoxPartialMatch->setToolTip("Excel列名称包含CSV字段名称");
    checkBoxPartialMatch->setChecked(false); // 默认不选中
    
    checkBoxChineseMatch = new QCheckBox("汉字匹配");
    checkBoxChineseMatch->setToolTip("基于汉字部分的智能匹配");
    checkBoxChineseMatch->setChecked(false); // 默认不选中
    
    checkBoxUseDisplayValue = new QCheckBox("显示值");
    checkBoxUseDisplayValue->setToolTip("当Excel数据为空时，使用显示数据列作为期望数据的默认值");
    checkBoxUseDisplayValue->setChecked(false); // 默认不选中
    
    matchOptionsLayout->addWidget(checkBoxExactMatch);
    matchOptionsLayout->addWidget(checkBoxPartialMatch);
    matchOptionsLayout->addWidget(checkBoxChineseMatch);
    matchOptionsLayout->addWidget(checkBoxUseDisplayValue);
    matchOptionsLayout->addStretch();
    
    formLayout->addRow(labelMatchOptions, matchOptionsLayout);
    
    mainLayout->addLayout(formLayout);
    
    // 预览表格
    tableWidgetPreview = new QTableWidget();
    tableWidgetPreview->setAlternatingRowColors(true);
    tableWidgetPreview->setSelectionBehavior(QAbstractItemView::SelectRows);
    tableWidgetPreview->horizontalHeader()->setStretchLastSection(true);
    connect(tableWidgetPreview, &QTableWidget::cellDoubleClicked, this, &ValueFileSelectionDialog::onTableDoubleClicked);
    mainLayout->addWidget(tableWidgetPreview);
    
    // 按钮布局
    buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    
    pushButtonConfirm = new QPushButton("导入数据");
    pushButtonConfirm->setEnabled(false);
    connect(pushButtonConfirm, &QPushButton::clicked, this, &ValueFileSelectionDialog::onConfirmClicked);
    buttonLayout->addWidget(pushButtonConfirm);
    
    pushButtonCancel = new QPushButton("取消");
    connect(pushButtonCancel, &QPushButton::clicked, this, &ValueFileSelectionDialog::onCancelClicked);
    buttonLayout->addWidget(pushButtonCancel);
    
    pushButtonDataMappingRules = new QPushButton("数据对应规则");
    pushButtonDataMappingRules->setStyleSheet("QPushButton { background-color: #27ae60; color: white; }");
    connect(pushButtonDataMappingRules, &QPushButton::clicked, this, &ValueFileSelectionDialog::onDataMappingRulesClicked);
    buttonLayout->addWidget(pushButtonDataMappingRules);
    
    pushButtonConfigureRules = new QPushButton("配置转换规则");
    pushButtonConfigureRules->setStyleSheet("QPushButton { background-color: #3498db; color: white; }");
    connect(pushButtonConfigureRules, &QPushButton::clicked, this, &ValueFileSelectionDialog::onConfigureRulesClicked);
    buttonLayout->addWidget(pushButtonConfigureRules);
    
    mainLayout->addLayout(buttonLayout);
}

void ValueFileSelectionDialog::loadWorksheetNames()
{
    QAxObject *excel = new QAxObject("Excel.Application");
    if (!excel) {
        QMessageBox::critical(this, "错误", "无法创建Excel应用程序对象！\n请确保已安装Microsoft Excel。");
        return;
    }
    
    excel->setProperty("Visible", false);
    
    QAxObject *workbooks = excel->querySubObject("Workbooks");
    QAxObject *workbook = workbooks->querySubObject("Open(const QString&)", excelFilePath);
    
    if (!workbook) {
        QMessageBox::critical(this, "错误", "无法打开Excel文件！");
        excel->dynamicCall("Quit()");
        delete excel;
        return;
    }
    
    QAxObject *worksheets = workbook->querySubObject("Worksheets");
    int worksheetCount = worksheets->property("Count").toInt();
    
    worksheetNames.clear();
    comboBoxWorksheet->clear();
    
    for (int i = 1; i <= worksheetCount; ++i) {
        QAxObject *worksheet = worksheets->querySubObject("Item(int)", i);
        if (worksheet) {
            QString worksheetName = worksheet->property("Name").toString();
            worksheetNames.append(worksheetName);
            comboBoxWorksheet->addItem(worksheetName);
        }
    }
    
    // 关闭Excel
    workbook->dynamicCall("Close()");
    excel->dynamicCall("Quit()");
    delete excel;
    
    if (enableDebugLog) {
        qDebug() << "发现工作表:" << worksheetNames;
    }
}

void ValueFileSelectionDialog::initializeExcelData()
{
    // 打开Excel文件并缓存
    openExcelFile();
    
    if (!cachedWorkbook) {
        return; // 打开失败
    }
    
    QAxObject *worksheets = cachedWorkbook->querySubObject("Worksheets");
    int worksheetCount = worksheets->property("Count").toInt();
    
    // 获取所有工作表名称
    worksheetNames.clear();
    comboBoxWorksheet->clear();
    
    for (int i = 1; i <= worksheetCount; ++i) {
        QAxObject *worksheet = worksheets->querySubObject("Item(int)", i);
        if (worksheet) {
            QString worksheetName = worksheet->property("Name").toString();
            worksheetNames.append(worksheetName);
            comboBoxWorksheet->addItem(worksheetName);
        }
    }
    
    if (enableDebugLog) {
        qDebug() << "发现工作表:" << worksheetNames;
    }
    
    // 加载第一个工作表的数据（如果存在）
    if (!worksheetNames.isEmpty()) {
        loadWorksheetData(worksheetNames.first());
        
        // 更新预览表格
        updatePreviewTable();
        
        if (enableDebugLog) {
            qDebug() << "Excel初始化完成，表头:" << excelHeaders;
            qDebug() << "数据行数:" << excelData.size();
        }
    }
}

void ValueFileSelectionDialog::openExcelFile()
{
    if (cachedExcel && cachedWorkbook && cachedFilePath == excelFilePath) {
        // Excel文件已经打开且是同一个文件，直接返回
        return;
    }
    
    // 关闭之前的Excel文件
    closeExcelFile();
    
    cachedExcel = new QAxObject("Excel.Application");
    if (!cachedExcel) {
        QMessageBox::critical(this, "错误", "无法创建Excel应用程序对象！\n请确保已安装Microsoft Excel。");
        return;
    }
    
    cachedExcel->setProperty("Visible", false);
    
    QAxObject *workbooks = cachedExcel->querySubObject("Workbooks");
    cachedWorkbook = workbooks->querySubObject("Open(const QString&)", excelFilePath);
    
    if (!cachedWorkbook) {
        QMessageBox::critical(this, "错误", "无法打开Excel文件！");
        cachedExcel->dynamicCall("Quit()");
        delete cachedExcel;
        cachedExcel = nullptr;
        return;
    }
    
    cachedFilePath = excelFilePath;
    
    if (enableDebugLog) {
        qDebug() << "Excel文件已缓存打开:" << excelFilePath;
    }
}

void ValueFileSelectionDialog::closeExcelFile()
{
    if (cachedWorkbook) {
        cachedWorkbook->dynamicCall("Close()");
        cachedWorkbook = nullptr;
    }
    
    if (cachedExcel) {
        cachedExcel->dynamicCall("Quit()");
        delete cachedExcel;
        cachedExcel = nullptr;
    }
    
    cachedFilePath.clear();
    
    if (enableDebugLog) {
        qDebug() << "Excel文件已关闭";
    }
}

void ValueFileSelectionDialog::loadWorksheetData(const QString &worksheetName)
{
    if (!cachedWorkbook) {
        qDebug() << "错误：Excel工作簿未打开";
        return;
    }
    
    QAxObject *worksheets = cachedWorkbook->querySubObject("Worksheets");
    QAxObject *worksheet = worksheets->querySubObject("Item(const QString&)", worksheetName);
    
    if (!worksheet) {
        QMessageBox::critical(this, "错误", QString("无法找到工作表: %1").arg(worksheetName));
        return;
    }
    
    // 获取已使用的区域
    QAxObject *usedRange = worksheet->querySubObject("UsedRange");
    QVariant rangeValue = usedRange->property("Value");
    
    // 解析Excel数据
    if (rangeValue.isValid()) {
        QVariantList rows = rangeValue.toList();
        
        if (!rows.isEmpty()) {
            if (rows.size() < 4) {
                qDebug() << "Excel文件行数不足，至少需要4行（标题、表头、单位、数据）";
                return;
            }
            
            // 第二行作为大类别表头
            QVariantList headerRow1 = rows[1].toList();
            QStringList bigCategories;
            for (const QVariant &cell : headerRow1) {
                bigCategories.append(cell.toString());
            }
            
            // 第三行作为小类别表头
            QVariantList headerRow2 = rows[2].toList();
            QStringList smallCategories;
            for (const QVariant &cell : headerRow2) {
                smallCategories.append(cell.toString());
            }
            
            // 合并第二行和第三行作为完整的表头
            excelHeaders.clear();
            int maxCols = qMax(bigCategories.size(), smallCategories.size());
            for (int i = 0; i < maxCols; ++i) {
                QString bigCategory = (i < bigCategories.size()) ? bigCategories[i] : "";
                QString smallCategory = (i < smallCategories.size()) ? smallCategories[i] : "";
                
                QString combinedHeader;
                if (!bigCategory.isEmpty() && !smallCategory.isEmpty()) {
                    if (bigCategory != smallCategory) {
                        combinedHeader = bigCategory + "-" + smallCategory;
                    } else {
                        combinedHeader = bigCategory;
                    }
                } else if (!bigCategory.isEmpty()) {
                    combinedHeader = bigCategory;
                } else if (!smallCategory.isEmpty()) {
                    combinedHeader = smallCategory;
                } else {
                    combinedHeader = QString("列%1").arg(i + 1);
                }
                
                excelHeaders.append(combinedHeader);
            }
            
            // 从第4行开始读取数据
            excelData.clear();
            for (int i = 3; i < rows.size(); ++i) {
                QVariantList row = rows[i].toList();
                QStringList rowData;
                for (const QVariant &cell : row) {
                    QString cellValue;
                    if (cell.type() == QVariant::Double || cell.type() == QVariant::Int) {
                        double num = cell.toDouble();
                        if (num == (int)num) {
                            cellValue = QString::number((int)num);
                        } else {
                            cellValue = QString::number(num);
                        }
                    } else {
                        cellValue = cell.toString();
                    }
                    rowData.append(cellValue);
                }
                excelData.append(rowData);
            }
            
            if (enableDebugLog) {
                qDebug() << "工作表" << worksheetName << "数据加载完成，表头:" << excelHeaders;
                qDebug() << "数据行数:" << excelData.size();
            }
        }
    }
}

void ValueFileSelectionDialog::loadExcelFile()
{
    // 默认加载第一个工作表
    loadExcelFile(1);
}

void ValueFileSelectionDialog::loadExcelFile(int worksheetIndex)
{
    QAxObject *excel = new QAxObject("Excel.Application");
    if (!excel) {
        QMessageBox::critical(this, "错误", "无法创建Excel应用程序对象！\n请确保已安装Microsoft Excel。");
        return;
    }
    
    excel->setProperty("Visible", false);
    
    QAxObject *workbooks = excel->querySubObject("Workbooks");
    QAxObject *workbook = workbooks->querySubObject("Open(const QString&)", excelFilePath);
    
    if (!workbook) {
        QMessageBox::critical(this, "错误", "无法打开Excel文件！");
        excel->dynamicCall("Quit()");
        delete excel;
        return;
    }
    
    QAxObject *worksheets = workbook->querySubObject("Worksheets");
    QAxObject *worksheet = worksheets->querySubObject("Item(int)", worksheetIndex);
    
    if (!worksheet) {
        QMessageBox::critical(this, "错误", "无法访问Excel工作表！");
        workbook->dynamicCall("Close()");
        excel->dynamicCall("Quit()");
        delete excel;
        return;
    }
    
    // 获取已使用的区域
    QAxObject *usedRange = worksheet->querySubObject("UsedRange");
    QVariant rangeValue = usedRange->property("Value");
    
    // 解析Excel数据
    if (rangeValue.isValid()) {
        QVariantList rows = rangeValue.toList();
        
        if (!rows.isEmpty()) {
            // 根据用户描述的Excel结构：
            // 第1行：无参照意义
            // 第2行：关键字比对信息（作为表头）
            // 第3行：单位信息
            // 第4行开始：实际数据
            
            if (rows.size() < 4) {
                qDebug() << "Excel文件行数不足，至少需要4行（标题、表头、单位、数据）";
                return;
            }
            
            // 第二行作为大类别表头
            QVariantList headerRow1 = rows[1].toList();
            QStringList bigCategories;
            for (const QVariant &cell : headerRow1) {
                bigCategories.append(cell.toString());
            }
            
            // 第三行作为小类别表头
            QVariantList headerRow2 = rows[2].toList();
            QStringList smallCategories;
            for (const QVariant &cell : headerRow2) {
                smallCategories.append(cell.toString());
            }
            
            // 合并第二行和第三行作为完整的表头
            excelHeaders.clear();
            int maxCols = qMax(bigCategories.size(), smallCategories.size());
            for (int i = 0; i < maxCols; ++i) {
                QString bigCategory = (i < bigCategories.size()) ? bigCategories[i] : "";
                QString smallCategory = (i < smallCategories.size()) ? smallCategories[i] : "";
                
                QString combinedHeader;
                if (!bigCategory.isEmpty() && !smallCategory.isEmpty()) {
                    // 如果大类别和小类别都不为空，合并显示
                    if (bigCategory != smallCategory) {
                        combinedHeader = bigCategory + "-" + smallCategory;
                    } else {
                        combinedHeader = bigCategory; // 如果相同，只显示一个
                    }
                } else if (!bigCategory.isEmpty()) {
                    combinedHeader = bigCategory;
                } else if (!smallCategory.isEmpty()) {
                    combinedHeader = smallCategory;
                } else {
                    combinedHeader = QString("列%1").arg(i + 1); // 默认列名
                }
                
                excelHeaders.append(combinedHeader);
            }
            
            // 输出调试信息（仅在调试模式下）
            if (enableDebugLog) {
                qDebug() << "Excel文件结构分析：";
                qDebug() << "第1行（标题）:" << (rows.size() > 0 ? "存在" : "不存在");
                qDebug() << "第2行（大类别）:" << bigCategories.join(", ");
                qDebug() << "第3行（小类别）:" << smallCategories.join(", ");
                qDebug() << "合并后表头:" << excelHeaders.join(", ");
                qDebug() << "数据从第4行开始，共" << (rows.size() - 3) << "行数据";
            }
            
            // 从第4行开始读取数据
            excelData.clear();
            for (int i = 3; i < rows.size(); ++i) {
                QVariantList row = rows[i].toList();
                QStringList rowData;
                for (const QVariant &cell : row) {
                    QString cellValue;
                    if (cell.type() == QVariant::Double || cell.type() == QVariant::Int) {
                        // 数字类型，转换为字符串时去掉小数点后的零
                        double num = cell.toDouble();
                        if (num == (int)num) {
                            cellValue = QString::number((int)num);
                        } else {
                            cellValue = QString::number(num);
                        }
                    } else {
                        cellValue = cell.toString();
                    }
                    rowData.append(cellValue);
                }
                excelData.append(rowData);
            }
        }
    }
    
    // 关闭Excel
    workbook->dynamicCall("Close()");
    excel->dynamicCall("Quit()");
    delete excel;
    
    // 更新预览表格
    updatePreviewTable();
    
    if (enableDebugLog) {
        qDebug() << "Excel文件加载完成，表头:" << excelHeaders;
        qDebug() << "数据行数:" << excelData.size();
        
        // 输出前几行数据用于调试
        for (int i = 0; i < qMin(5, excelData.size()); ++i) {
            qDebug() << "第" << i << "行数据:" << excelData[i];
        }
    }
}

void ValueFileSelectionDialog::loadExcelFile(const QString &worksheetName)
{
    QAxObject *excel = new QAxObject("Excel.Application");
    if (!excel) {
        QMessageBox::critical(this, "错误", "无法创建Excel应用程序对象！\n请确保已安装Microsoft Excel。");
        return;
    }
    
    excel->setProperty("Visible", false);
    
    QAxObject *workbooks = excel->querySubObject("Workbooks");
    QAxObject *workbook = workbooks->querySubObject("Open(const QString&)", excelFilePath);
    
    if (!workbook) {
        QMessageBox::critical(this, "错误", "无法打开Excel文件！");
        excel->dynamicCall("Quit()");
        delete excel;
        return;
    }
    
    QAxObject *worksheets = workbook->querySubObject("Worksheets");
    QAxObject *worksheet = worksheets->querySubObject("Item(const QString&)", worksheetName);
    
    if (!worksheet) {
        QMessageBox::critical(this, "错误", QString("无法访问工作表: %1！").arg(worksheetName));
        workbook->dynamicCall("Close()");
        excel->dynamicCall("Quit()");
        delete excel;
        return;
    }
    
    // 获取已使用的区域
    QAxObject *usedRange = worksheet->querySubObject("UsedRange");
    QVariant rangeValue = usedRange->property("Value");
    
    // 解析Excel数据
    if (rangeValue.isValid()) {
        QVariantList rows = rangeValue.toList();
        
        if (!rows.isEmpty()) {
            // 根据用户描述的Excel结构：
            // 第1行：无参照意义
            // 第2行：关键字比对信息（作为表头）
            // 第3行：单位信息
            // 第4行开始：实际数据
            
            if (rows.size() < 4) {
                qDebug() << "Excel文件行数不足，至少需要4行（标题、表头、单位、数据）";
                return;
            }
            
            // 第二行作为大类别表头
            QVariantList headerRow1 = rows[1].toList();
            QStringList bigCategories;
            for (const QVariant &cell : headerRow1) {
                bigCategories.append(cell.toString());
            }
            
            // 第三行作为小类别表头
            QVariantList headerRow2 = rows[2].toList();
            QStringList smallCategories;
            for (const QVariant &cell : headerRow2) {
                smallCategories.append(cell.toString());
            }
            
            // 合并第二行和第三行作为完整的表头
            excelHeaders.clear();
            int maxCols = qMax(bigCategories.size(), smallCategories.size());
            for (int i = 0; i < maxCols; ++i) {
                QString bigCategory = (i < bigCategories.size()) ? bigCategories[i] : "";
                QString smallCategory = (i < smallCategories.size()) ? smallCategories[i] : "";
                
                QString combinedHeader;
                if (!bigCategory.isEmpty() && !smallCategory.isEmpty()) {
                    // 如果大类别和小类别都不为空，合并显示
                    if (bigCategory != smallCategory) {
                        combinedHeader = bigCategory + "-" + smallCategory;
                    } else {
                        combinedHeader = bigCategory; // 如果相同，只显示一个
                    }
                } else if (!bigCategory.isEmpty()) {
                    combinedHeader = bigCategory;
                } else if (!smallCategory.isEmpty()) {
                    combinedHeader = smallCategory;
                } else {
                    combinedHeader = QString("列%1").arg(i + 1); // 默认列名
                }
                
                excelHeaders.append(combinedHeader);
            }
            
            // 输出调试信息（仅在调试模式下）
            if (enableDebugLog) {
                qDebug() << "Excel文件结构分析（工作表:" << worksheetName << "）：";
                qDebug() << "第1行（标题）:" << (rows.size() > 0 ? "存在" : "不存在");
                qDebug() << "第2行（大类别）:" << bigCategories.join(", ");
                qDebug() << "第3行（小类别）:" << smallCategories.join(", ");
                qDebug() << "合并后表头:" << excelHeaders.join(", ");
                qDebug() << "数据从第4行开始，共" << (rows.size() - 3) << "行数据";
            }
            
            // 从第4行开始读取数据
            excelData.clear();
            for (int i = 3; i < rows.size(); ++i) {
                QVariantList row = rows[i].toList();
                QStringList rowData;
                for (const QVariant &cell : row) {
                    QString cellValue;
                    if (cell.type() == QVariant::Double || cell.type() == QVariant::Int) {
                        // 数字类型，转换为字符串时去掉小数点后的零
                        double num = cell.toDouble();
                        if (num == (int)num) {
                            cellValue = QString::number((int)num);
                        } else {
                            cellValue = QString::number(num);
                        }
                    } else {
                        cellValue = cell.toString();
                    }
                    rowData.append(cellValue);
                }
                excelData.append(rowData);
            }
        }
    }
    
    // 关闭Excel
    workbook->dynamicCall("Close()");
    excel->dynamicCall("Quit()");
    delete excel;
    
    // 更新预览表格
    updatePreviewTable();
    
    if (enableDebugLog) {
        qDebug() << "Excel文件加载完成（工作表:" << worksheetName << "），表头:" << excelHeaders;
        qDebug() << "数据行数:" << excelData.size();
        
        // 输出前几行数据用于调试
        for (int i = 0; i < qMin(5, excelData.size()); ++i) {
            qDebug() << "第" << i << "行数据:" << excelData[i];
        }
    }
}

void ValueFileSelectionDialog::updatePreviewTable()
{
    if (excelHeaders.isEmpty()) {
        return;
    }
    
    // 暂时禁用更新以提高性能
    tableWidgetPreview->setUpdatesEnabled(false);
    
    tableWidgetPreview->setColumnCount(excelHeaders.size());
    
    // 为表头添加列号标识（A、B、C、D等）
    QStringList headersWithColumnLabels;
    for (int i = 0; i < excelHeaders.size(); ++i) {
        QString columnLabel;
        // 生成列号标识：A, B, C, ..., Z, AA, AB, ...
        int colIndex = i;
        do {
            columnLabel = QChar('A' + (colIndex % 26)) + columnLabel;
            colIndex = colIndex / 26 - 1;
        } while (colIndex >= 0);
        
        QString headerWithLabel = QString("%1\n%2").arg(columnLabel).arg(excelHeaders[i]);
        headersWithColumnLabels.append(headerWithLabel);
    }
    
    tableWidgetPreview->setHorizontalHeaderLabels(headersWithColumnLabels);
    
    // 限制显示行数以提高性能
    int displayRows = qMin(excelData.size(), MAX_PREVIEW_ROWS);
    tableWidgetPreview->setRowCount(displayRows);
    
    // 批量创建表格项
    for (int row = 0; row < displayRows; ++row) {
        const QStringList &rowData = excelData[row];
        for (int col = 0; col < rowData.size() && col < excelHeaders.size(); ++col) {
            QTableWidgetItem *item = new QTableWidgetItem(rowData[col]);
            item->setFlags(item->flags() & ~Qt::ItemIsEditable); // 设为只读
            tableWidgetPreview->setItem(row, col, item);
        }
    }
    
    // 如果数据行数超过显示限制，添加提示
    if (excelData.size() > MAX_PREVIEW_ROWS) {
        // 在状态栏或其他地方显示提示信息
        if (enableDebugLog) {
            qDebug() << QString("预览表格仅显示前%1行，总共%2行数据")
                        .arg(MAX_PREVIEW_ROWS).arg(excelData.size());
        }
    }
    
    // 隐藏第一列
    if (tableWidgetPreview->columnCount() > 0) {
        tableWidgetPreview->setColumnHidden(0, true);
    }
    
    // 重新启用更新并调整列宽
    tableWidgetPreview->setUpdatesEnabled(true);
    tableWidgetPreview->resizeColumnsToContents();
}

void ValueFileSelectionDialog::onDeviceModelChanged()
{
    QString selectedModel = comboBoxDeviceModel->currentText();
    if (selectedModel != originalDeviceModel) {
        QMessageBox::warning(this, "警告", 
            QString("选择的设备型号(%1)与当前设备型号(%2)不一致！\n请选择正确的设备型号。")
            .arg(selectedModel).arg(originalDeviceModel));
        
        comboBoxDeviceModel->setCurrentText(originalDeviceModel);
    }
}

void ValueFileSelectionDialog::onCabinetNumberChanged()
{
    QString cabinetNumber = lineEditCabinetNumber->text().trimmed();
    if (enableDebugLog) {
        qDebug() << "柜号输入变化:" << cabinetNumber;
    }
    
    if (cabinetNumber.isEmpty()) {
        if (enableDebugLog) {
            qDebug() << "柜号为空，禁用确认按钮";
        }
        pushButtonConfirm->setEnabled(false);
        return;
    }
    
    // 查找柜号对应的行
    int cabinetRow = findCabinetRow(cabinetNumber);
    if (enableDebugLog) {
        qDebug() << "查找结果，行号:" << cabinetRow;
    }
    
    if (cabinetRow >= 0) {
        if (enableDebugLog) {
            qDebug() << "找到匹配行，启用确认按钮并高亮显示";
        }
        // 高亮显示找到的行
        tableWidgetPreview->selectRow(cabinetRow);
        pushButtonConfirm->setEnabled(true);
        
        // 滚动到该行
        tableWidgetPreview->scrollToItem(tableWidgetPreview->item(cabinetRow, 0));
    } else {
        if (enableDebugLog) {
            qDebug() << "未找到匹配行，禁用确认按钮";
        }
        tableWidgetPreview->clearSelection();
        pushButtonConfirm->setEnabled(false);
    }
}

void ValueFileSelectionDialog::onConfirmClicked()
{
    if (!validateDeviceModel()) {
        return;
    }
    
    QString cabinetNumber = lineEditCabinetNumber->text().trimmed();
    if (cabinetNumber.isEmpty()) {
        QMessageBox::warning(this, "警告", "请输入柜号！");
        return;
    }
    
    int cabinetRow = findCabinetRow(cabinetNumber);
    if (cabinetRow < 0) {
        QMessageBox::warning(this, "警告", "未找到指定的柜号！");
        return;
    }
    
    // 执行数据匹配
    matchDataToParseItems();
    
    // 应用转换规则
    applyConversionRules();
    
    // 发射数据导入完成信号，通知主界面立即更新表格
    emit dataImported();
    
    // 显示导入成功消息，但不关闭窗口，允许多次导入
    QMessageBox::information(this, "成功", "定值数据已成功导入到期望数据（定值文件读取）列！\n\n窗口保持打开状态，您可以继续导入其他数据或操作其他界面。");
    
    // 清空柜号输入框，准备下次导入
    lineEditCabinetNumber->clear();
    pushButtonConfirm->setEnabled(false);
    tableWidgetPreview->clearSelection();
}

void ValueFileSelectionDialog::onCancelClicked()
{
    reject();
}

QList<ParseItem> ValueFileSelectionDialog::getUpdatedParseItems() const
{
    return updatedParseItems;
}

QStringList ValueFileSelectionDialog::getExcelHeaders() const
{
    return excelHeaders;
}

bool ValueFileSelectionDialog::validateDeviceModel()
{
    QString selectedModel = comboBoxDeviceModel->currentText();
    if (selectedModel != originalDeviceModel) {
        QMessageBox::critical(this, "错误", 
            QString("设备型号不匹配！\n当前设备型号: %1\n选择的设备型号: %2")
            .arg(originalDeviceModel).arg(selectedModel));
        return false;
    }
    return true;
}

int ValueFileSelectionDialog::findCabinetRow(const QString &cabinetNumber)
{
    qDebug() << "查找柜号:" << cabinetNumber << "数据行数:" << excelData.size();
    
    // 在第一列中查找柜号（假设柜号在第一列）
    for (int row = 0; row < excelData.size(); ++row) {
        if (!excelData[row].isEmpty()) {
            QString cellValue = excelData[row][0].trimmed();
            qDebug() << "第" << row << "行第0列数据:" << cellValue << "比较结果:" << (cellValue == cabinetNumber);
            if (cellValue == cabinetNumber) {
                qDebug() << "在第" << row << "行第0列找到匹配的柜号";
                return row;
            }
        }
    }
    
    // 如果第一列没找到，尝试在所有列中查找
    for (int row = 0; row < excelData.size(); ++row) {
        const QStringList &rowData = excelData[row];
        for (int col = 0; col < rowData.size(); ++col) {
            QString cellValue = rowData[col].trimmed();
            if (cellValue == cabinetNumber) {
                qDebug() << "在第" << row << "行第" << col << "列找到匹配的柜号";
                return row;
            }
        }
    }
    
    qDebug() << "未找到柜号:" << cabinetNumber;
    return -1; // 未找到
}

void ValueFileSelectionDialog::matchDataToParseItems()
{
    QString cabinetNumber = lineEditCabinetNumber->text().trimmed();
    int cabinetRow = findCabinetRow(cabinetNumber);
    
    if (cabinetRow < 0 || cabinetRow >= excelData.size()) {
        return;
    }
    
    const QStringList &rowData = excelData[cabinetRow];
    
    // 1. 首先清空所有解析项的相关字段
    for (int i = 0; i < updatedParseItems.size(); ++i) {
        updatedParseItems[i].expectedData.clear(); // 清空期望数据列
        updatedParseItems[i].expectedDataFromFile.clear(); // 清空期望数据(定值文件读取)列
        updatedParseItems[i].featureSelection = false; // 清空特性选择列
        updatedParseItems[i].enableProtection = false; // 清空投退选择列
    }
    
    // 2. 记录导入信息用于日志输出
    QStringList logMessages;
    if (enableUILog) {
        logMessages << QString("=== 定值文件数据导入开始 ===");
        logMessages << QString("柜号: %1 (第%2行)").arg(cabinetNumber).arg(cabinetRow + 1);
        
        // 显示当前选择的映射规则信息
        QString selectedMappingRule = comboBoxMappingRule->currentText();
        if (selectedMappingRule != "请选择对应规则" && !dataMappingRules.isEmpty()) {
            logMessages << QString("使用数据对应表: %1 (包含%2条映射规则)").arg(selectedMappingRule).arg(dataMappingRules.size());
        } else {
            logMessages << QString("使用自动匹配模式 (未选择数据对应表)");
        }
        
        logMessages << QString("Excel表头: %1").arg(excelHeaders.join(", "));
        logMessages << QString("数据行内容: %1").arg(rowData.join(", "));
        logMessages << QString("");
    }
    
    int matchedCount = 0;
    int totalCount = updatedParseItems.size();
    
    // 3. 遍历解析项，优先使用数据对应表映射规则，然后使用自动匹配
    for (int i = 0; i < updatedParseItems.size(); ++i) {
        ParseItem &item = updatedParseItems[i];
        
        // 使用CSV第一列的字段进行匹配
        int columnIndex = -1;
        QString matchType = "";
        QString csvFieldName = item.name; // CSV第一列的字段名称
        
        // 1. 首先尝试使用数据对应表映射规则
        if (!dataMappingRules.isEmpty()) {
            for (const DataMappingRule &rule : dataMappingRules) {
                // 检查CSV行号列表是否包含当前项（CSV行号从1开始）
                if (rule.csvRows.contains(i + 1)) {
                        // 找到对应的Excel列
                        if (!rule.excelColumn.isEmpty()) {
                            // 解析Excel列标识（可能是列名、列号或Excel列字母）
                            bool isNumber = false;
                            int colNum = rule.excelColumn.toInt(&isNumber);
                            
                            if (isNumber && colNum > 0 && colNum <= excelHeaders.size()) {
                                // 如果是数字，转换为索引（从1开始转为从0开始）
                                columnIndex = colNum - 1;
                                matchType = QString("数据对应表映射(行%1->列%2)").arg(i + 1).arg(colNum);
                            } else {
                                // 检查是否是Excel列字母格式（如A、B、C等）
                                QString excelCol = rule.excelColumn.toUpper().trimmed();
                                bool isExcelColumn = true;
                                int excelColIndex = 0;
                                
                                // 将Excel列字母转换为索引（A=0, B=1, C=2, ...）
                                for (int k = 0; k < excelCol.length(); ++k) {
                                    QChar ch = excelCol[k];
                                    if (ch >= 'A' && ch <= 'Z') {
                                        excelColIndex = excelColIndex * 26 + (ch.unicode() - 'A' + 1);
                                    } else {
                                        isExcelColumn = false;
                                        break;
                                    }
                                }
                                
                                if (isExcelColumn && excelColIndex > 0 && excelColIndex <= excelHeaders.size()) {
                                    // Excel列字母转换为索引（从1开始转为从0开始）
                                    columnIndex = excelColIndex - 1;
                                    matchType = QString("数据对应表映射(行%1->Excel列%2)").arg(i + 1).arg(excelCol);
                                } else {
                                    // 如果不是Excel列字母，尝试作为列名查找
                                    columnIndex = excelHeaders.indexOf(rule.excelColumn);
                                    if (columnIndex >= 0) {
                                        matchType = QString("数据对应表映射(行%1->列名'%2')").arg(i + 1).arg(rule.excelColumn);
                                    }
                                }
                            }
                        }
                        break;
                    }
                }
            }
        
        // 2. 如果映射规则未找到匹配，根据复选框状态尝试额外匹配方式
        if (columnIndex < 0 && checkBoxExactMatch->isChecked()) {
            // 精确匹配：CSV字段名称与Excel列名称完全一致
            columnIndex = excelHeaders.indexOf(csvFieldName);
            if (columnIndex >= 0) {
                matchType = "CSV字段名称精确匹配";
            }
        }
        
        // 3. 部分匹配（Excel列名称包含CSV字段名称）
        if (columnIndex < 0 && checkBoxPartialMatch->isChecked()) {
            for (int j = 0; j < excelHeaders.size(); ++j) {
                const QString &header = excelHeaders[j].trimmed();
                if (!header.isEmpty() && header.contains(csvFieldName, Qt::CaseInsensitive)) {
                    columnIndex = j;
                    matchType = "Excel列名称包含CSV字段";
                    break;
                }
            }
        }
        
        // 4. 汉字匹配（基于汉字部分的智能匹配）
        if (columnIndex < 0 && checkBoxChineseMatch->isChecked()) {
            // 提取CSV字段名称中的汉字
            QString csvChineseChars;
            for (const QChar &ch : csvFieldName) {
                if (ch.script() == QChar::Script_Han) {
                    csvChineseChars += ch;
                }
            }
            
            if (!csvChineseChars.isEmpty()) {
                for (int j = 0; j < excelHeaders.size(); ++j) {
                    const QString &header = excelHeaders[j].trimmed();
                    if (!header.isEmpty()) {
                        // 提取Excel列名称中的汉字
                        QString excelChineseChars;
                        for (const QChar &ch : header) {
                            if (ch.script() == QChar::Script_Han) {
                                excelChineseChars += ch;
                            }
                        }
                        
                        // 检查汉字匹配
                        if (!excelChineseChars.isEmpty() && 
                            (excelChineseChars.contains(csvChineseChars) || 
                             csvChineseChars.contains(excelChineseChars))) {
                            columnIndex = j;
                            matchType = "汉字智能匹配";
                            break;
                        }
                    }
                }
            }
        }
        
        // 如果未找到匹配
        if (columnIndex < 0) {
            matchType = "无匹配";
        }

        
        // 如果找到匹配的列，更新期望数据(定值文件读取)
        if (columnIndex >= 0) {
            if (columnIndex < rowData.size()) {
                QString value = rowData[columnIndex].trimmed();
                if (!value.isEmpty()) {
                    item.expectedDataFromFile = value;
                    matchedCount++;
                    if (enableUILog) {
                        logMessages << QString("✓ [%1] CSV字段'%2' -> Excel列[%3](%4) = %5 [%6]")
                            .arg(i + 1)
                            .arg(csvFieldName)
                            .arg(columnIndex + 1)
                            .arg(excelHeaders[columnIndex])
                            .arg(value)
                            .arg(matchType);
                    }
                    qDebug() << "匹配成功:" << csvFieldName << "->" << value;
                } else {
                    if (enableUILog) {
                        logMessages << QString("○ [%1] CSV字段'%2' -> Excel列[%3](%4) = (空值) [%5]")
                            .arg(i + 1)
                            .arg(csvFieldName)
                            .arg(columnIndex + 1)
                            .arg(excelHeaders[columnIndex])
                            .arg(matchType);
                    }
                }
            } else {
                if (enableUILog) {
                    logMessages << QString("⚠ [%1] CSV字段'%2' -> Excel列[%3](%4) 超出数据范围 [%5] (数据行只有%6列)")
                        .arg(i + 1)
                        .arg(csvFieldName)
                        .arg(columnIndex + 1)
                        .arg(excelHeaders[columnIndex])
                        .arg(matchType)
                        .arg(rowData.size());
                }
                qDebug() << "列索引超出范围:" << columnIndex << "数据行列数:" << rowData.size();
            }
        } else {
            if (enableUILog) {
                logMessages << QString("× [%1] CSV字段'%2' -> 未找到匹配的Excel列")
                    .arg(i + 1)
                    .arg(csvFieldName);
            }
        }
    }
    
    if (enableUILog) {
        logMessages << QString("");
        logMessages << QString("=== 导入统计 ===");
        logMessages << QString("成功匹配: %1/%2 项").arg(matchedCount).arg(totalCount);
        logMessages << QString("=== 定值数据已成功导入到期望数据（定值文件读取）列！ ===");
    }
    
    // 4. 将日志信息传递给父窗口显示（仅在启用UI日志时）
    if (enableUILog) {
        emit logMessageGenerated(logMessages.join("\n"));
    } else {
        // 只输出简要的导入结果
        QString summaryMessage = QString("=== 定值数据导入完成 === 柜号: %1, 成功匹配: %2/%3 项")
            .arg(cabinetNumber).arg(matchedCount).arg(totalCount);
        emit logMessageGenerated(summaryMessage);
    }
    
    qDebug() << "数据匹配完成，柜号:" << cabinetNumber << "行号:" << cabinetRow << "匹配项数:" << matchedCount;
}

void ValueFileSelectionDialog::onTableDoubleClicked(int row, int column)
{
    Q_UNUSED(column);
    
    // 检查行号是否有效
    if (row < 0 || row >= excelData.size()) {
        return;
    }
    
    // 查找"柜号"列的索引
    int cabinetColumnIndex = -1;
    for (int i = 0; i < excelHeaders.size(); ++i) {
        if (excelHeaders[i].contains("柜号", Qt::CaseInsensitive)) {
            cabinetColumnIndex = i;
            break;
        }
    }
    
    // 如果没找到"柜号"列，使用第一列作为备选
    if (cabinetColumnIndex == -1) {
        cabinetColumnIndex = 0;
        qDebug() << "未找到柜号列，使用第一列作为柜号";
    } else {
        qDebug() << "找到柜号列，索引:" << cabinetColumnIndex << "列名:" << excelHeaders[cabinetColumnIndex];
    }
    
    // 获取该行指定列的数据（柜号）
    const QStringList &rowData = excelData[row];
    if (cabinetColumnIndex < rowData.size()) {
        QString cabinetNumber = rowData[cabinetColumnIndex].trimmed();
        if (!cabinetNumber.isEmpty()) {
            // 将柜号填入到输入框中
            lineEditCabinetNumber->setText(cabinetNumber);
            
            // 触发柜号变化事件，这会自动高亮该行并启用确认按钮
            onCabinetNumberChanged();
            
            qDebug() << "双击表格行" << row << "，自动填入柜号:" << cabinetNumber << "来自列" << cabinetColumnIndex;
        }
    }
}

void ValueFileSelectionDialog::onDataMappingRulesClicked()
{
    // 从parseItems中提取CSV行名称
    QStringList csvRowNames;
    for (const ParseItem &item : originalParseItems) {
        csvRowNames.append(item.name);
    }
    
    DataMappingRulesDialog dialog("", excelHeaders, csvRowNames, this);
    dialog.setMappingRules(dataMappingRules);
    
    if (dialog.exec() == QDialog::Accepted) {
        dataMappingRules = dialog.getMappingRules();
        qDebug() << "数据映射规则已更新，共" << dataMappingRules.size() << "条规则";
        
        // 刷新映射配置下拉框
        loadMappingConfigs();
    }
}

void ValueFileSelectionDialog::onWorksheetChanged()
{
    int currentIndex = comboBoxWorksheet->currentIndex();
    if (currentIndex >= 0 && currentIndex < worksheetNames.size()) {
        QString selectedWorksheet = worksheetNames[currentIndex];
        if (enableDebugLog) {
            qDebug() << "切换到工作表:" << selectedWorksheet;
        }
        
        // 使用缓存的Excel快速加载工作表数据
        loadWorksheetData(selectedWorksheet);
        
        // 更新预览表格
        updatePreviewTable();
        
        // 重新匹配数据
        matchDataToParseItems();
    }
}

void ValueFileSelectionDialog::onConfigureRulesClicked()
{
    DataConversionRulesDialog dialog(this);
    dialog.setRules(conversionRules);
    
    if (dialog.exec() == QDialog::Accepted) {
        conversionRules = dialog.getRules();
        qDebug() << "转换规则已更新，共" << conversionRules.size() << "条规则";
    }
}

void ValueFileSelectionDialog::loadConversionRules()
{
    conversionRules = DataConversionRulesDialog::loadRulesFromFile();
    if (conversionRules.isEmpty()) {
        // 如果没有保存的规则，创建默认规则
        DataConversionRule rule;
        
        // DEF TIME -> 1, 特性选择=true
        rule.sourcePattern = "DEF TIME";
        rule.targetValue = "1";
        rule.featureSelection = true;
        rule.enableProtection = false;
        rule.description = "定时特性，期望值为1，特性选择选中";
        rule.isNumberPattern = false;
        conversionRules.append(rule);
        
        // INV TIME -> 0, 特性选择=true
        rule.sourcePattern = "INV TIME";
        rule.targetValue = "0";
        rule.featureSelection = true;
        rule.enableProtection = false;
        rule.description = "反时限特性，期望值为0，特性选择选中";
        rule.isNumberPattern = false;
        conversionRules.append(rule);
        
        // TRIP -> 1, 投退标志=true
        rule.sourcePattern = "TRIP";
        rule.targetValue = "1";
        rule.featureSelection = false;
        rule.enableProtection = true;
        rule.description = "跳闸，期望值为1，投退标志选中";
        rule.isNumberPattern = false;
        conversionRules.append(rule);
        
        // OFF -> 0, 投退标志=false
        rule.sourcePattern = "OFF";
        rule.targetValue = "0";
        rule.featureSelection = false;
        rule.enableProtection = false;
        rule.description = "关闭，期望值为0，投退标志不选中";
        rule.isNumberPattern = false;
        conversionRules.append(rule);
        
        // ALARM -> 2, 投退标志=true
        rule.sourcePattern = "ALARM";
        rule.targetValue = "2";
        rule.featureSelection = false;
        rule.enableProtection = true;
        rule.description = "告警，期望值为2，投退标志选中";
        rule.isNumberPattern = false;
        conversionRules.append(rule);
        
        // 数字+字母模式 -> 提取数字
        rule.sourcePattern = "数字+字母";
        rule.targetValue = "提取数字";
        rule.featureSelection = false;
        rule.enableProtection = false;
        rule.description = "数字+字母形式，提取数字部分作为期望值";
        rule.isNumberPattern = true;
        conversionRules.append(rule);
    }
    
    qDebug() << "加载了" << conversionRules.size() << "条转换规则";
}

void ValueFileSelectionDialog::applyConversionRules()
{
    if (conversionRules.isEmpty()) {
        qDebug() << "没有转换规则，跳过规则应用";
        return;
    }
    
    QStringList logMessages;
    if (enableUILog) {
        logMessages << QString("=== 应用数据转换规则 ===");
    }
    
    int appliedCount = 0;
    int totalWithData = 0;
    
    // 遍历所有解析项，对所有项应用转换规则（包括空数据）
    for (int i = 0; i < updatedParseItems.size(); ++i) {
        ParseItem &item = updatedParseItems[i];
        
        // 处理所有项，包括空数据
        totalWithData++;
        
        QString originalValue = item.expectedDataFromFile;
        QString targetValue;
        bool featureSelection = false;
        bool enableProtection = false;
        
        // 应用转换规则
        DataConversionRulesDialog::applyRulesToData(originalValue, targetValue, featureSelection, enableProtection, conversionRules, item.minValue, item.actualData, checkBoxUseDisplayValue->isChecked());
        
        if (!targetValue.isEmpty()) {
            // 更新期望数据
            item.expectedData = targetValue;
            item.featureSelection = featureSelection;
            item.enableProtection = enableProtection;
            
            appliedCount++;
            if (enableUILog) {
                logMessages << QString("✓ [%1] %2: '%3' -> 期望数据='%4', 特性选择=%5, 投退标志=%6")
                    .arg(i + 1)
                    .arg(item.name)
                    .arg(originalValue)
                    .arg(targetValue)
                    .arg(featureSelection ? "是" : "否")
                    .arg(enableProtection ? "是" : "否");
            }
        } else {
            if (enableUILog) {
                logMessages << QString("○ [%1] %2: '%3' -> 未找到匹配规则")
                    .arg(i + 1)
                    .arg(item.name)
                    .arg(originalValue);
            }
        }
    }
    
    if (enableUILog) {
        logMessages << QString("");
        logMessages << QString("=== 转换规则应用统计 ===");
        logMessages << QString("有数据的项: %1 项").arg(totalWithData);
        logMessages << QString("成功转换: %1 项").arg(appliedCount);
        logMessages << QString("=== 数据转换规则应用完成！ ===");
    }
    
    // 将日志信息传递给父窗口显示（仅在启用UI日志时）
    if (enableUILog) {
        emit logMessageGenerated(logMessages.join("\n"));
    } else {
        // 只输出简要的转换结果
        QString summaryMessage = QString("=== 数据转换规则应用完成 === 处理: %1 项, 成功转换: %2 项")
            .arg(totalWithData).arg(appliedCount);
        emit logMessageGenerated(summaryMessage);
    }
    
    qDebug() << "转换规则应用完成，处理了" << totalWithData << "项数据，成功转换" << appliedCount << "项";
}

void ValueFileSelectionDialog::loadMappingConfigs()
{
    // 清空下拉框
    comboBoxMappingRule->clear();
    comboBoxMappingRule->addItem("请选择对应规则");
    
    // 加载所有可用的映射配置
    QList<DataMappingConfig> configs = DataMappingRulesDialog::loadAllConfigs();
    
    for (const DataMappingConfig &config : configs) {
        QString displayText = QString("%1 (%2)").arg(config.configName, config.deviceModel);
        comboBoxMappingRule->addItem(displayText, config.configName);
    }
    
    qDebug() << "已加载" << configs.size() << "个映射配置";
}

void ValueFileSelectionDialog::onMappingRuleChanged()
{
    QString selectedText = comboBoxMappingRule->currentText();
    QString configName = comboBoxMappingRule->currentData().toString();
    
    if (configName.isEmpty() || selectedText == "请选择对应规则") {
        currentMappingConfigName.clear();
        dataMappingRules.clear();
        qDebug() << "清空映射规则选择";
        return;
    }
    
    // 加载选中的映射配置
    DataMappingConfig config = DataMappingRulesDialog::loadConfig(configName);
    if (!config.configName.isEmpty()) {
        currentMappingConfigName = configName;
        dataMappingRules = config.mappings;
        qDebug() << "已选择映射规则:" << configName << "，包含" << dataMappingRules.size() << "条映射";
        
        // 可以在这里添加重新加载数据的逻辑
        // 如果需要立即应用新的映射规则，可以调用相关方法
    } else {
        QMessageBox::warning(this, "警告", QString("无法加载映射配置: %1").arg(configName));
    }
}