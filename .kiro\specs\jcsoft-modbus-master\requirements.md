# JcSoft Modbus Master 需求文档

## 介绍

JcSoft是一个基于Qt开发的Modbus RTU主站应用工具，主要用于工业系统通讯调试。该应用支持双串口通信，提供控制器和保护装置的测试配置功能，以及定值召唤等高级功能。

## 需求

### 需求 1 - 串口通信管理

**用户故事:** 作为一个工程师，我希望能够配置和管理双串口连接，以便与不同的Modbus设备进行通信。

#### 验收标准

1. WHEN 用户启动应用时 THEN 系统应该自动扫描并显示可用的串口列表
2. WHEN 用户选择串口参数（波特率、校验位、数据位、停止位）时 THEN 系统应该验证参数的有效性
3. WHEN 用户连接串口时 THEN 系统应该显示连接状态并在状态栏提供反馈
4. WHEN 串口连接失败时 THEN 系统应该显示详细的错误信息
5. WHEN 用户断开串口时 THEN 系统应该安全关闭连接并更新UI状态

### 需求 2 - Modbus协议通信

**用户故事:** 作为一个工程师，我希望能够发送和接收Modbus RTU命令，以便与从站设备进行数据交换。

#### 验收标准

1. WHEN 用户发送Modbus命令时 THEN 系统应该正确构建RTU帧并计算CRC校验
2. WHEN 系统接收到响应数据时 THEN 应该验证CRC校验并解析数据内容
3. WHEN 通信超时时 THEN 系统应该提供超时提示并允许重试
4. WHEN 发送原始十六进制数据时 THEN 系统应该正确解析并发送字节数据
5. WHEN 接收到数据时 THEN 系统应该以十六进制格式显示原始报文

### 需求 3 - 测试配置管理

**用户故事:** 作为一个测试工程师，我希望能够配置控制器和保护装置的测试映射关系，以便进行自动化测试。

#### 验收标准

1. WHEN 用户添加映射关系时 THEN 系统应该验证串口和设备的匹配性（COMA对应控制器，COMB对应保护装置）
2. WHEN 用户保存映射配置时 THEN 系统应该支持JSON和XML格式的导入导出
3. WHEN 系统生成测试步骤时 THEN 应该根据设备类型自动生成相应的Modbus命令
4. WHEN 用户编辑测试步骤时 THEN 系统应该提供直观的编辑界面
5. WHEN 执行测试时 THEN 系统应该按顺序执行步骤并记录结果

### 需求 4 - 自动化测试执行

**用户故事:** 作为一个测试工程师，我希望能够自动执行测试序列，以便提高测试效率和准确性。

#### 验收标准

1. WHEN 用户启动测试时 THEN 系统应该按照预定义的步骤顺序执行
2. WHEN 测试步骤执行时 THEN 系统应该发送命令并等待响应
3. WHEN 接收到响应时 THEN 系统应该与期望结果进行比较
4. WHEN 测试步骤失败时 THEN 系统应该记录失败原因并继续执行后续步骤
5. WHEN 测试完成时 THEN 系统应该生成详细的测试报告

### 需求 5 - 流程图可视化

**用户故事:** 作为一个用户，我希望能够通过流程图查看测试执行过程，以便直观了解测试进度和状态。

#### 验收标准

1. WHEN 系统生成测试步骤时 THEN 应该自动创建对应的流程图
2. WHEN 测试执行时 THEN 流程图应该高亮显示当前执行的步骤
3. WHEN 测试步骤完成时 THEN 对应的节点应该显示通过或失败状态
4. WHEN 用户双击流程图节点时 THEN 应该打开步骤编辑对话框
5. WHEN 用户右键点击流程图时 THEN 应该显示上下文菜单提供操作选项

### 需求 6 - 定值召唤功能

**用户故事:** 作为一个保护装置调试工程师，我希望能够读取和写入设备定值，以便进行设备参数配置。

#### 验收标准

1. WHEN 用户选择设备型号时 THEN 系统应该加载对应的定值配置文件
2. WHEN 用户读取定值时 THEN 系统应该发送读取命令并解析返回的数据
3. WHEN 用户修改定值时 THEN 系统应该验证数值范围并提供输入校验
4. WHEN 用户写入定值时 THEN 系统应该构建写入命令并确认写入结果
5. WHEN 系统检测到定值不一致时 THEN 应该高亮显示并提供批量写入选项

### 需求 7 - 数据记录和日志

**用户故事:** 作为一个用户，我希望系统能够记录所有通信数据和操作日志，以便进行问题诊断和数据分析。

#### 验收标准

1. WHEN 发送数据时 THEN 系统应该在日志中记录发送的原始数据和时间戳
2. WHEN 接收数据时 THEN 系统应该在日志中记录接收的原始数据和时间戳
3. WHEN 发生错误时 THEN 系统应该记录详细的错误信息和上下文
4. WHEN 用户清空日志时 THEN 系统应该清除显示区域的内容
5. WHEN 系统运行时 THEN 应该使用不同颜色区分发送和接收的数据

### 需求 8 - 配置文件管理

**用户故事:** 作为一个用户，我希望能够保存和加载各种配置文件，以便在不同项目间复用配置。

#### 验收标准

1. WHEN 用户保存配置时 THEN 系统应该将配置保存到dev_doc文件夹
2. WHEN 用户加载配置时 THEN 系统应该从dev_doc文件夹读取配置文件
3. WHEN 配置文件不存在时 THEN 系统应该提供友好的错误提示
4. WHEN 系统自动保存时 THEN 应该使用延迟保存机制避免频繁IO操作
5. WHEN 文件操作失败时 THEN 系统应该在日志中显示详细的错误信息

### 需求 9 - 用户界面交互

**用户故事:** 作为一个用户，我希望应用界面直观易用，提供良好的用户体验。

#### 验收标准

1. WHEN 用户启动应用时 THEN 界面应该显示清晰的功能分区和操作按钮
2. WHEN 用户进行操作时 THEN 系统应该提供即时的状态反馈
3. WHEN 发生错误时 THEN 系统应该显示用户友好的错误消息
4. WHEN 用户调整窗口大小时 THEN 界面元素应该合理缩放和布局
5. WHEN 用户使用右键菜单时 THEN 应该提供相关的上下文操作选项

### 需求 10 - 报告生成

**用户故事:** 作为一个测试工程师，我希望能够生成测试报告，以便记录测试结果和分析数据。

#### 验收标准

1. WHEN 测试完成时 THEN 系统应该能够生成HTML格式的测试报告
2. WHEN 生成报告时 THEN 报告应该包含测试步骤、结果和统计信息
3. WHEN 用户导出报告时 THEN 系统应该支持PDF格式导出
4. WHEN 报告包含图表时 THEN 应该正确显示测试数据的可视化
5. WHEN 保存报告时 THEN 系统应该提供文件名和路径选择选项