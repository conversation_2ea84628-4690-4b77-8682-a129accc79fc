#include "mappingwindow.h"
#include "crcutils.h"
#include "ui_mappingwindow.h"
#include <QCheckBox>
#include <QDateTime>
#include <QEvent>
#include <QFileDialog>
#include <QHBoxLayout>
#include <QInputDialog>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QLabel>
#include <QMessageBox>
#include <QMouseEvent>
#include <QPageLayout>
#include <QPageSize>
#include <QPrintDialog>
#include <QPrinter>
#include <QPushButton>
#include <QRegularExpression>
#include <QStringConverter>
#include <QTabWidget>
#include <QTextCursor>
#include <QTextEdit>
#include <QTextStream>
#include <QTimer>
#include <QVBoxLayout>
#include <QXmlStreamReader>
#include <QXmlStreamWriter>
#include <cmath>

MappingWindow::MappingWindow(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::MappingWindow) {
  ui->setupUi(this);
  initUI();
  initCommandMaps();
  QPushButton *reportBtn = this->findChild<QPushButton *>("testReportButton");
  if (reportBtn) {
    connect(reportBtn, &QPushButton::clicked, this,
            &MappingWindow::onTestReportButtonClicked);
  }
}

MappingWindow::~MappingWindow() { delete ui; }

void MappingWindow::initUI() {
  // 获取UI中的控件引用
  serialPortComboBox = ui->serialPortComboBox;
  deviceComboBox = ui->deviceComboBox;
  mappingList = ui->mappingList;
  view = ui->view;
  testStepsList = ui->testStepsList;
  // 新增的UI组件引用
  logTextEdit = ui->logTextEdit;
  testButton = ui->testButton;
  clearLogButton = ui->clearLogButton;
  // 创建场景并设置给视图
  scene = new QGraphicsScene(this);
  view->setScene(scene);

  // 为流程图视图设置右键菜单
  view->setContextMenuPolicy(Qt::CustomContextMenu);
  connect(view, &QGraphicsView::customContextMenuRequested, this,
          &MappingWindow::onFlowchartContextMenuRequested);

  // 初始化测试定时器
  testTimer = new QTimer(this);
  testTimeoutTimer = new QTimer(this);
  currentTestIndex = 0;
  isRunningTests = false;

  // 保存控制器和保护装置选项到列表中
  // 添加控制器选项
  QStringList deviceDescriptions = {"DCS OFF 遥控合/DCS OFF 遥控分",
                                    "SIS OFF 遥控合/SIS OFF 遥控分",
                                    "OTHER OFF 遥控合/OTHER OFF 遥控分",
                                    "手、自动 遥控合/手、自动 遥控分",
                                    "SB1 手合 遥控合/SB1 手合 遥控分",
                                    "SB2 手分 遥控合/SB2 手分 遥控分",
                                    "DCS ON 遥控合/DCS ON 遥控分",
                                    "ACS 自启动合 遥控合/ACS 自启动合 遥控分",
                                    "DCS 准备好 遥控合/DCS 准备好 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "CLEAR 清除输出"};

  for (int i = 1; i <= 16; ++i) {
    QString desc = deviceDescriptions[i - 1];
    if (i == 16) {
      controllerDevices.append(
          QString("J%1 (%2)").arg(i).arg(desc.split(" ")[0]));
      // CLEAR 清除输出只添加一个命令（J16C）
    } else {
      controllerDevices.append(
          QString("J%1 (%2)").arg(i).arg(desc.split("/")[0]));
      controllerDevices.append(
          QString("J%1 (%2)").arg(i).arg(desc.split("/")[1]));
    }
  }

  // 添加保护装置选项
  for (int i = 1; i <= 13; ++i)
    protectionDevices.append(QString("DI%1").arg(i));
  for (int i = 1; i <= 5; ++i)
    protectionDevices.append(QString("DO%1").arg(i));
  protectionDevices.append("RUN状态");
  protectionDevices.append("IA");
  protectionDevices.append("IB");
  protectionDevices.append("IC");

  // 从文件加载保存的设备列表（如果存在）
  loadDeviceListsFromFile();

  // 初始化设备下拉框（默认显示控制器设备）
  deviceComboBox->clear();
  deviceComboBox->addItems(controllerDevices);

  // 为设备下拉框设置右键菜单
  deviceComboBox->setContextMenuPolicy(Qt::CustomContextMenu);
  connect(deviceComboBox, &QComboBox::customContextMenuRequested, this,
          &MappingWindow::showDeviceContextMenu);

  // 连接信号和槽
  connect(ui->addButton, &QPushButton::clicked, this,
          &MappingWindow::addMapping);
  connect(ui->saveButton, &QPushButton::clicked, this,
          &MappingWindow::saveMapping);
  connect(ui->loadButton, &QPushButton::clicked, this,
          &MappingWindow::loadMapping);
  connect(ui->undoButton, &QPushButton::clicked, this, &MappingWindow::undo);
  connect(ui->redoButton, &QPushButton::clicked, this, &MappingWindow::redo);
  connect(ui->resetButton, &QPushButton::clicked, this,
          &MappingWindow::resetTestState);
  connect(ui->editButton, &QPushButton::clicked, this,
          &MappingWindow::editTestStep);
  connect(ui->detailsButton, &QPushButton::clicked, this,
          &MappingWindow::showTestStepDetails);
  connect(serialPortComboBox,
          QOverload<int>::of(&QComboBox::currentIndexChanged), this,
          &MappingWindow::onSerialPortChanged);
  connect(ui->globalCrc16CheckBox, &QCheckBox::toggled, this,
          &MappingWindow::onGlobalCrc16CheckBoxToggled);

  // 为测试流程列表添加双击事件
  connect(testStepsList, &QListWidget::itemDoubleClicked, this,
          &MappingWindow::onTestStepsListDoubleClicked);

  // 为流程图添加双击事件（通过安装事件过滤器）
  view->installEventFilter(this);

  // 新增的测试相关信号槽连接
  connect(testButton, &QPushButton::clicked, this,
          &MappingWindow::onTestButtonClicked);
  connect(clearLogButton, &QPushButton::clicked, this,
          &MappingWindow::onClearLogButtonClicked);
  connect(testTimer, &QTimer::timeout, this, &MappingWindow::runNextTest);
  connect(testTimeoutTimer, &QTimer::timeout, this,
          &MappingWindow::onTestTimeout);

  // 连接串口信号
  connect(serialA, &ModbusSerialA::communicationMessageReceived, this,
          &MappingWindow::handleSerialAMessageReceived);
  connect(serialB, &ModbusSerialB::communicationMessageReceived, this,
          &MappingWindow::handleSerialBMessageReceived);

  // 设置分割器的默认分割比例，映射配置窗口比测试流程窗口宽50像素
  QList<int> sizes;
  int totalWidth = this->width() > 0 ? this->width() : 900; // 使用默认宽度900
  int mappingWidth =
      (totalWidth * 1) / 3 + 50; // 原来1:2的比例，现在映射配置多50像素
  int testStepsWidth = totalWidth - mappingWidth;
  sizes << mappingWidth << testStepsWidth;
  ui->displaySplitter->setSizes(sizes);

  // 初始化日志
  logMessage("TEST配置界面已初始化", "INFO");

  mappingList->setContextMenuPolicy(Qt::CustomContextMenu);
  connect(mappingList, &QListWidget::customContextMenuRequested, this,
          &MappingWindow::onMappingListContextMenuRequested);

  flowchartOnlyCheckBox = ui->flowchartOnlyCheckBox;
  flowchartOnlyCheckBox->setChecked(true);
  connect(flowchartOnlyCheckBox, &QCheckBox::checkStateChanged, this,
          &MappingWindow::onFlowchartOnlyCheckBoxChanged);
  // 默认只显示流程图
  onFlowchartOnlyCheckBoxChanged(Qt::Checked);
}

void MappingWindow::initCommandMaps() {
  // 初始化控制器遥控命令
  QStringList deviceDescriptions = {"DCS OFF 遥控合/DCS OFF 遥控分",
                                    "SIS OFF 遥控合/SIS OFF 遥控分",
                                    "OTHER OFF 遥控合/OTHER OFF 遥控分",
                                    "手、自动 遥控合/手、自动 遥控分",
                                    "SB1 手合 遥控合/SB1 手合 遥控分",
                                    "SB2 手分 遥控合/SB2 手分 遥控分",
                                    "DCS ON 遥控合/DCS ON 遥控分",
                                    "ACS 自启动合 遥控合/ACS 自启动合 遥控分",
                                    "DCS 准备好 遥控合/DCS 准备好 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "备用 遥控合/备用 遥控分",
                                    "CLEAR 清除输出"};

  for (int i = 1; i <= 16; ++i) {
    QString desc = deviceDescriptions[i - 1];
    if (i == 16) {
      QString jc = QString("J%1 (%2)").arg(i).arg(desc.split(" ")[0]);
      controllerRemoteOnCommands[jc] = "Addr 06 00 00 80 00";
      // CLEAR 清除输出只添加一个命令
    } else {
      QString jh = QString("J%1 (%2)").arg(i).arg(desc.split("/")[0]);
      QString jt = QString("J%1 (%2)").arg(i).arg(desc.split("/")[1]);
      QString hex = QString("%1").arg(i - 1, 2, 16, QChar('0')).toUpper();
      controllerRemoteOnCommands[jh] = "Addr 05 00 " + hex + " FF 00";
      controllerRemoteOffCommands[jt] = "Addr 05 00 " + hex + " 00 00";
    }
  }

  // 初始化保护装置遥控命令
  protectionRemoteCommands["DO1"] = "Addr 05 00 01 FF 00";
  protectionRemoteCommands["DO2"] = "Addr 05 00 07 FF 00";
  protectionRemoteCommands["DO3"] = "Addr 05 00 00 FF 00";
  protectionRemoteCommands["DO4"] = "Addr 05 00 02 FF 00";
  protectionRemoteCommands["DO5"] = "Addr 05 00 08 FF 00";

  // 初始化保护装置读数命令
  for (int i = 1; i <= 8; ++i) {
    QString di = QString("DI%1").arg(i);
    QString hex = QString("%1").arg(i - 1, 2, 16, QChar('0')).toUpper();
    protectionReadCommands[di] = "Addr 02 00 " + hex + " 00 01";
  }
  // DI9-DI13的特殊处理
  protectionReadCommands["DI9"] = "Addr 02 00 08 00 01";
  protectionReadCommands["DI10"] = "Addr 02 00 04 00 01";
  protectionReadCommands["DI11"] = "Addr 02 00 05 00 01";
  protectionReadCommands["DI12"] = "Addr 02 00 06 00 01";
  protectionReadCommands["DI13"] = "Addr 02 00 07 00 01";

  // RUN状态、IA、IB、IC等命令
  protectionReadCommands["RUN状态"] = "Addr 04 00 14 00 01";
  protectionReadCommands["IA"] = "Addr 04 00 00 00 01";
  protectionReadCommands["IB"] = "Addr 04 00 01 00 01";
  protectionReadCommands["IC"] = "Addr 04 00 01 00 01";
}

void MappingWindow::onSerialPortChanged(int index) {
  deviceComboBox->clear();

  // 根据选择的串口更新设备列表
  if (index == 0) { // 主控软件(COMA) - 只能选择控制器
    deviceComboBox->addItems(controllerDevices);
  } else { // 主控软件(COMB) - 只能选择保护装置
    deviceComboBox->addItems(protectionDevices);
  }
}

void MappingWindow::addMapping() {
  QString serialPort = serialPortComboBox->currentText();
  QString device = deviceComboBox->currentText();
  QString deviceType =
      (serialPortComboBox->currentIndex() == 0) ? "controller" : "protection";

  // 验证输入
  if (serialPort.isEmpty() || device.isEmpty()) {
    QMessageBox::warning(this, "输入错误", "请选择串口和设备");
    return;
  }

  // 保存当前状态到撤销栈（用于撤销操作）
  QList<MappingRelation> oldMappingRelations = mappingRelations;

  // 检查是否已存在相同的映射
  bool mappingExists = false;
  int existingIndex = -1;

  for (int i = 0; i < mappingRelations.size(); ++i) {
    if (mappingRelations[i].serialPort == serialPort &&
        mappingRelations[i].device == device) {
      mappingExists = true;
      existingIndex = i;
      break;
    }
  }

  if (mappingExists) {
    int reply =
        QMessageBox::question(this, "映射已存在",
                              QString("%1 到 %2 的映射已存在，是否替换？")
                                  .arg(serialPort)
                                  .arg(device),
                              QMessageBox::Yes | QMessageBox::No);
    if (reply == QMessageBox::No) {
      return;
    }

    // 移除旧映射
    mappingRelations.removeAt(existingIndex);
  }

  // 添加新映射
  mappingRelations.append(MappingRelation(serialPort, device, deviceType));

  // 保存操作到撤销栈
  undoMappingStack.push(oldMappingRelations);
  // 清空重做栈（因为进行了新操作）
  redoMappingStack.clear();

  // 更新列表显示
  updateMappingList();

  // 更新图形显示
  updateGraphicalMapping(-1);

  // 自动生成测试流程
  generateTestSteps();
}

void MappingWindow::updateMappingList() {
  mappingList->clear();

  // 使用新的映射关系结构
  for (const MappingRelation &rel : mappingRelations) {
    QString displayText;
    if (rel.deviceType == "controller") {
      displayText =
          QString("%1 -> %2 (控制器)").arg(rel.serialPort).arg(rel.device);
    } else {
      displayText =
          QString("%1 -> %2 (保护装置)").arg(rel.serialPort).arg(rel.device);
    }
    mappingList->addItem(displayText);
  }
}

void MappingWindow::importConfig() {
  QString fileName = QFileDialog::getOpenFileName(
      this, "导入TEST配置", QCoreApplication::applicationDirPath() + "/dev_doc",
      "JSON文件 (*.json);;XML文件 (*.xml)");
  if (fileName.isEmpty()) {
    return;
  }

  FileFormat format =
      fileName.endsWith(".json", Qt::CaseInsensitive) ? JSON : XML;
  QFile file(fileName);

  if (!file.open(QIODevice::ReadOnly)) {
    QMessageBox::warning(this, "导入错误",
                         "无法打开文件：" + file.errorString());
    return;
  }

  try {
    // 清空当前映射
    mapping.clear();

    if (format == JSON) {
      // 读取JSON格式
      QByteArray jsonData = file.readAll();
      QJsonDocument doc = QJsonDocument::fromJson(jsonData);
      if (doc.isNull() || !doc.isObject()) {
        throw std::runtime_error("无效的JSON格式");
      }

      QJsonObject root = doc.object();
      if (root.contains("testSteps") && root["testSteps"].isArray()) {
        // 优先从 testSteps 导入
        QJsonArray testStepsArray = root["testSteps"].toArray();
        // 清空当前映射和测试步骤，因为我们将从 testSteps 重建
        mapping.clear();
        testSteps.clear();
        // 将 testStepsArray 转换为内部的 testSteps 结构
        // 这里需要一个辅助函数或者直接处理，将符合统一格式的 testStepsArray
        // 中的对象 转换并添加到 this->testSteps，并从中重建 mapping
        // 为简化，此处假设 testStepsArray 中的每个对象都包含足够信息重建
        // mapping 和 testSteps 例如，每个 step 对象可能包含 description,
        // sentDataA, expectReceiveA 等 以及用于重建 mapping 的 controllerPoint
        // 和 protectionPoint
        for (const QJsonValue &stepVal : testStepsArray) {
          QJsonObject stepObj = stepVal.toObject();
          // 假设 stepObj 包含 controllerPoint 和 protectionPoint 用于重建
          // mapping
          if (stepObj.contains("controllerPoint") &&
              stepObj.contains("protectionPoint")) {
            mapping.insert(stepObj["controllerPoint"].toString(),
                           stepObj["protectionPoint"].toString());
          }
          // 将 stepObj 添加到 this->testSteps (TestStep
          // 结构可能也需要更新以匹配统一格式)
          TestStep currentStep;
          currentStep.type =
              stepObj.value("type").toString("info"); // 默认为 info
          currentStep.command =
              stepObj.value("command").toString(); // Modbus 命令或 UART 数据
          currentStep.notes = stepObj.value("description")
                                  .toString(); // 使用 description 作为 notes
          currentStep.sendData =
              stepObj.value("sentDataA")
                  .toString(); // 假设 UART 的发送数据在 sentDataA
          currentStep.expectReceiveData =
              stepObj.value("expectReceiveA")
                  .toString(); // 假设 UART 的期望接收在 expectReceiveA
          currentStep.delay = stepObj.value("delay").toInt(0);
          currentStep.configType =
              stepObj.value("configType").toString(); // "modbus" or "uart"
          // 其他新格式字段的读取
          currentStep.sentDataB = stepObj.value("sentDataB").toString();
          currentStep.expectReceiveB =
              stepObj.value("expectReceiveB").toString();
          currentStep.actualReceiveA =
              stepObj.value("actualReceiveA").toString();
          currentStep.actualReceiveB =
              stepObj.value("actualReceiveB").toString();
          currentStep.crc16 = stepObj.value("crc16").toString();
          currentStep.stationId = stepObj.value("stationId").toInt(0);
          currentStep.functionCode = stepObj.value("functionCode").toInt(0);
          currentStep.targetSerialPort =
              stepObj.value("targetSerialPort").toString("A");

          testSteps.append(currentStep);
        }
        // generateTestSteps(); // 如果 testSteps
        // 被直接填充，可能不需要再调用这个
      } else if (root.contains("mappings")) {
        // 回退到只导入 mappings
        QJsonObject mappingsObj = root["mappings"].toObject();
        for (auto it = mappingsObj.begin(); it != mappingsObj.end(); ++it) {
          mapping.insert(it.key(), it.value().toString());
        }
        generateTestSteps(); // 从 mapping 生成 testSteps
      } else {
        throw std::runtime_error(
            "无效的JSON格式，缺少 'mappings' 或 'testSteps' 字段");
      }
    } else {
      // 读取XML格式
      QXmlStreamReader xml(&file);

      while (!xml.atEnd() && !xml.hasError()) {
        QXmlStreamReader::TokenType token = xml.readNext();

        if (token == QXmlStreamReader::StartElement &&
            xml.name() == "mapping") {
          QString controller = xml.attributes().value("controller").toString();
          QString protection = xml.attributes().value("protection").toString();
          if (!controller.isEmpty() && !protection.isEmpty()) {
            mapping.insert(controller, protection);
          }
        }
      }

      if (xml.hasError()) {
        throw std::runtime_error(
            QString("XML解析错误：%1").arg(xml.errorString()).toStdString());
      }
    }

    // 更新界面显示
    updateMappingList();
    updateGraphicalMapping(-1);
    generateTestSteps();
    // 补充：同步期望数据字段
    for (auto &step : testSteps) {
      if (step.expectReceiveA.isEmpty() && !step.expectReceiveData.isEmpty()) {
        step.expectReceiveA = step.expectReceiveData;
      }
      if (step.expectReceiveData.isEmpty() && !step.expectReceiveA.isEmpty()) {
        step.expectReceiveData = step.expectReceiveA;
      }
    }

    QMessageBox::information(this, "导入成功", "配置导入成功");

  } catch (const std::exception &e) {
    QMessageBox::critical(this, "导入错误",
                          QString("导入配置时发生错误：%1").arg(e.what()));
  }
}

void MappingWindow::exportConfig() {
  if (mapping.isEmpty()) {
    QMessageBox::warning(this, "导出警告", "没有配置数据可以导出");
    return;
  }

  QString fileName = QFileDialog::getSaveFileName(
      this, "导出TEST配置", QCoreApplication::applicationDirPath() + "/dev_doc",
      "JSON文件 (*.json);;XML文件 (*.xml)");
  if (fileName.isEmpty()) {
    return;
  }

  FileFormat format =
      fileName.endsWith(".json", Qt::CaseInsensitive) ? JSON : XML;
  QFile file(fileName);

  if (!file.open(QIODevice::WriteOnly)) {
    QMessageBox::warning(this, "导出错误",
                         "无法创建文件：" + file.errorString());
    return;
  }

  try {
    if (format == JSON) {
      // 写入JSON格式
      QJsonObject root;
      QJsonObject mappings;

      QMapIterator<QString, QString> i(mapping);
      while (i.hasNext()) {
        i.next();
        mappings.insert(i.key(), i.value());
      }

      root["mappings"] = mappings;

      // 同时导出 testSteps 数组，采用统一格式
      QJsonArray testStepsArray;
      generateTestSteps(); // 确保 testSteps 是最新的
      for (const TestStep &step : std::as_const(this->testSteps)) {
        QJsonObject stepObj;
        stepObj["type"] = step.type;
        stepObj["command"] = step.command;
        stepObj["description"] = step.notes; // 使用 notes 作为 description
        stepObj["delay"] = step.delay;
        stepObj["configType"] = step.configType; // "modbus" or "uart"

        // 统一格式字段
        // 对于 MappingWindow 生成的步骤，主要填充串口A相关字段
        // sentDataA 和 expectReceiveA 会根据 step.sendData 和
        // step.expectReceiveData 填充
        stepObj["sentDataA"] = step.sendData;
        stepObj["expectReceiveA"] = step.expectReceiveData;
        stepObj["sentDataB"] = ""; // MappingWindow 通常只处理一个流向
        stepObj["expectReceiveB"] = "";
        stepObj["actualReceiveA"] = ""; // 实际接收在测试时填充
        stepObj["actualReceiveB"] = "";
        stepObj["crc16"] = "";    // CRC 通常在发送前计算，或如果数据包含则导出
        stepObj["stationId"] = 0; // UART模式下可能无此概念，或需特殊处理
        stepObj["functionCode"] = 0;
        stepObj["targetSerialPort"] = "A"; // 默认为A，或根据实际情况调整

        // 如果是Modbus类型的步骤，可以尝试填充 stationId 和 functionCode
        if (step.configType == "modbus") {
          // 假设 command 字段格式如 "Addr:01 Func:03 Data:00000001"
          QStringList parts = step.command.split(QRegularExpression(
              "[\\s:]+")); // Corrected escape for backslash s
          bool sidOk, fcOk;
          if (parts.contains("Addr") &&
              parts.indexOf("Addr") + 1 < parts.size()) {
            stepObj["stationId"] =
                parts[parts.indexOf("Addr") + 1].toInt(&sidOk, 16);
          }
          if (parts.contains("Func") &&
              parts.indexOf("Func") + 1 < parts.size()) {
            stepObj["functionCode"] =
                parts[parts.indexOf("Func") + 1].toInt(&fcOk, 16);
          }
          // CRC16的计算和填充逻辑可以放在这里，如果需要的话
        }

        testStepsArray.append(stepObj);
      }
      root["testSteps"] = testStepsArray;

      QJsonDocument doc(root);
      file.write(doc.toJson(QJsonDocument::Indented));

    } else {
      // 写入XML格式
      QXmlStreamWriter xml(&file);
      xml.setAutoFormatting(true);

      xml.writeStartDocument();
      xml.writeStartElement("mappings");

      QMapIterator<QString, QString> i(mapping);
      while (i.hasNext()) {
        i.next();
        xml.writeStartElement("mapping");
        xml.writeAttribute("controller", i.key());
        xml.writeAttribute("protection", i.value());
        xml.writeEndElement();
      }

      xml.writeEndElement(); // mappings
      xml.writeEndDocument();
    }

    QMessageBox::information(this, "导出成功", "配置已成功导出");

  } catch (const std::exception &e) {
    QMessageBox::critical(this, "导出错误",
                          QString("导出配置时发生错误：%1").arg(e.what()));
  }
}

void MappingWindow::updateGraphicalMapping(int highlightStep) {
  // 清除现有的线条
  for (QGraphicsLineItem *line : mappingLines) {
    scene->removeItem(line);
    delete line;
  }
  mappingLines.clear();

  // 清除场景
  scene->clear();

  // 设置场景大小 - 适应蛇形布局，增加高度以容纳更多内容
  int width = 1400;
  int height = 700;
  scene->setSceneRect(0, 0, width, height);

  // 添加渐变背景
  QLinearGradient gradient(0, 0, 0, height);
  gradient.setColorAt(0, QColor(240, 248, 255)); // 浅蓝色
  gradient.setColorAt(1, QColor(255, 255, 255)); // 白色
  scene->setBackgroundBrush(QBrush(gradient));

  // 确保测试步骤是最新的
  if (testSteps.isEmpty()) {
    generateTestSteps();
  }

  // 如果仍然没有测试步骤，显示提示信息
  if (testSteps.isEmpty()) {
    QGraphicsTextItem *hintText =
        scene->addText("请先添加映射关系以生成测试执行流程");
    hintText->setPos(width / 2 - 100, height / 2);
    return;
  }

  // 计算蛇形布局的位置
  int nodeRadius = 40;
  int nodeSpacingX = 120;
  int nodeSpacingY = 100;
  int startY = 80;
  int leftMargin = 100;
  int nodesPerRow = 7;
  auto getNodePosition = [&](int index) -> QPointF {
    int row = index / nodesPerRow;
    int col = index % nodesPerRow;
    if (row % 2 == 1) {
      col = nodesPerRow - 1 - col;
    }
    int x = leftMargin + col * nodeSpacingX;
    int y = startY + row * nodeSpacingY;
    return QPointF(x, y);
  };
  // 绘制开始节点
  QPointF startPos = getNodePosition(0);
  QRadialGradient startGradient(startPos.x(), startPos.y() + nodeRadius / 2,
                                nodeRadius / 2);
  startGradient.setColorAt(0, QColor(144, 238, 144));
  startGradient.setColorAt(1, QColor(34, 139, 34));
  scene->addEllipse(startPos.x() - nodeRadius / 2, startPos.y(), nodeRadius,
                    nodeRadius, QPen(QColor(0, 100, 0), 3),
                    QBrush(startGradient));
  QGraphicsTextItem *startText = scene->addText("开始");
  startText->setFont(QFont("Microsoft YaHei", 10, QFont::Bold));
  startText->setDefaultTextColor(Qt::white);
  QRectF startRect = startText->boundingRect();
  startText->setPos(startPos.x() - startRect.width() / 2,
                    startPos.y() + nodeRadius / 2 - startRect.height() / 2);
  // 绘制每个测试步骤 - 新的矩形卡片样式
  for (int i = 0; i < testSteps.size(); ++i) {
    const TestStep &step = testSteps[i];
    QPointF currentPos = getNodePosition(i + 1);
    QPointF prevPos = getNodePosition(i);

    // 绘制连接线
    QPen linePen(QColor(100, 149, 237), 3);
    linePen.setStyle(Qt::SolidLine);
    QGraphicsLineItem *line =
        scene->addLine(prevPos.x(), prevPos.y() + 25, currentPos.x(),
                       currentPos.y() + 25, linePen);
    mappingLines.append(line);

    // 绘制箭头
    double angle =
        atan2(currentPos.y() - prevPos.y(), currentPos.x() - prevPos.x());
    QPointF arrowTip = QPointF(currentPos.x() - 25 * cos(angle),
                               currentPos.y() + 25 - 25 * sin(angle));
    QPolygonF arrowHead;
    arrowHead << arrowTip
              << QPointF(arrowTip.x() - 8 * cos(angle - 0.4),
                         arrowTip.y() - 8 * sin(angle - 0.4))
              << QPointF(arrowTip.x() - 8 * cos(angle + 0.4),
                         arrowTip.y() - 8 * sin(angle + 0.4));
    scene->addPolygon(arrowHead, QPen(QColor(70, 130, 180), 2),
                      QBrush(QColor(100, 149, 237)));

    // 新的矩形卡片样式
    int cardWidth = 80;
    int cardHeight = 50;
    QRectF cardRect(currentPos.x() - cardWidth / 2, currentPos.y(), cardWidth,
                    cardHeight);

    // 确定背景颜色（优化配色方案）
    QColor backgroundColor;
    if (i == highlightStep) {
      // 执行到这一步时的高亮背景色
      backgroundColor = QColor(100, 200, 255); // 亮蓝色，表示当前执行步骤
    } else if (i == selectedNodeIndex) {
      // 选中状态的背景色
      backgroundColor = QColor(255, 215, 0); // 金黄色，表示选中状态
    } else {
      // 默认背景色
      backgroundColor = QColor(220, 220, 220); // 浅灰色，统一的默认背景
    }

    // 绘制主卡片
    QPen cardPen(Qt::black, 2);
    QGraphicsRectItem *mainCard =
        scene->addRect(cardRect, cardPen, QBrush(backgroundColor));
    mainCard->setData(0, i); // 存储测试步骤索引

    // 设置工具提示
    QString tip =
        QString("步骤%1\n设备:%2\n命令:%3\n期望:%4\n实际:%5\n状态:%"
                "6\n\n双击编辑此步骤")
            .arg(i + 1)
            .arg(step.device)
            .arg(step.command)
            .arg(step.expected)
            .arg(step.actual)
            .arg(step.passed ? "通过"
                             : (step.actual.isEmpty() ? "未执行" : "失败"));
    mainCard->setToolTip(tip);

    // 解析设备名称和说明
    QString deviceName = step.device;
    QString deviceDescription = "";

    // 检查是否有括号说明
    int bracketStart = deviceName.indexOf('(');
    int bracketEnd = deviceName.indexOf(')');
    if (bracketStart >= 0 && bracketEnd > bracketStart) {
      deviceDescription =
          deviceName.mid(bracketStart + 1, bracketEnd - bracketStart - 1);
      deviceName = deviceName.left(bracketStart).trimmed();
    }

    // 左上角设备名称
    QGraphicsTextItem *deviceNameText = scene->addText(deviceName);
    deviceNameText->setFont(QFont("Microsoft YaHei", 8, QFont::Bold));
    deviceNameText->setDefaultTextColor(Qt::black);
    deviceNameText->setData(0, i);
    deviceNameText->setPos(cardRect.left() + 3, cardRect.top() + 2);

    // 右上角步骤数
    QGraphicsTextItem *stepNumberText = scene->addText(QString::number(i + 1));
    stepNumberText->setFont(QFont("Microsoft YaHei", 8, QFont::Bold));
    stepNumberText->setDefaultTextColor(Qt::black);
    stepNumberText->setData(0, i);
    QRectF stepNumRect = stepNumberText->boundingRect();
    stepNumberText->setPos(cardRect.right() - stepNumRect.width() - 3,
                           cardRect.top() + 2);

    // 中心状态圆点
    int dotRadius = 6;
    QPointF dotCenter(cardRect.center().x(), cardRect.center().y() - 5);

    // 优化状态圆点颜色
    QColor dotColor;
    if (step.passed) {
      dotColor = QColor(34, 139, 34); // 深绿色 - 测试通过
    } else if (!step.actual.isEmpty() && !step.passed) {
      dotColor = QColor(255, 165, 0); // 橙色 - 测试失败
    } else {
      dotColor = QColor(220, 20, 60); // 深红色 - 初始状态/未执行
    }

    QGraphicsEllipseItem *statusDot = scene->addEllipse(
        dotCenter.x() - dotRadius, dotCenter.y() - dotRadius, dotRadius * 2,
        dotRadius * 2, QPen(Qt::black, 1), QBrush(dotColor));
    statusDot->setData(0, i);

    // 红点下面的设备说明
    if (!deviceDescription.isEmpty()) {
      QGraphicsTextItem *descriptionText = scene->addText(deviceDescription);
      descriptionText->setFont(QFont("Microsoft YaHei", 6));
      descriptionText->setDefaultTextColor(Qt::black);
      descriptionText->setData(0, i);
      QRectF descRect = descriptionText->boundingRect();
      descriptionText->setPos(cardRect.center().x() - descRect.width() / 2,
                              dotCenter.y() + dotRadius + 2);
    } else {
      // 如果没有说明，显示命令类型
      QString commandType = "";
      if (step.type == "remote_on")
        commandType = "遥控合";
      else if (step.type == "remote_off")
        commandType = "遥控分";
      else if (step.type == "read")
        commandType = "读取";
      else if (step.type == "remote")
        commandType = "遥控";
      else
        commandType = step.command;

      QGraphicsTextItem *commandText = scene->addText(commandType);
      commandText->setFont(QFont("Microsoft YaHei", 6));
      commandText->setDefaultTextColor(Qt::black);
      commandText->setData(0, i);
      QRectF cmdRect = commandText->boundingRect();
      commandText->setPos(cardRect.center().x() - cmdRect.width() / 2,
                          dotCenter.y() + dotRadius + 2);
    }
  }
  // 绘制结束节点
  QPointF endPos = getNodePosition(testSteps.size() + 1);
  QPointF lastPos = getNodePosition(testSteps.size());
  QPen finalLinePen(QColor(100, 149, 237), 3);
  finalLinePen.setStyle(Qt::SolidLine);
  QGraphicsLineItem *finalLine = scene->addLine(
      lastPos.x(), lastPos.y() + 25, endPos.x(), endPos.y() + 25, finalLinePen);
  mappingLines.append(finalLine);
  double finalAngle = atan2(endPos.y() - lastPos.y(), endPos.x() - lastPos.x());
  QPointF finalArrowTip = QPointF(endPos.x() - 25 * cos(finalAngle),
                                  endPos.y() + 25 - 25 * sin(finalAngle));
  QPolygonF finalArrowHead;
  finalArrowHead << finalArrowTip
                 << QPointF(finalArrowTip.x() - 8 * cos(finalAngle - 0.4),
                            finalArrowTip.y() - 8 * sin(finalAngle - 0.4))
                 << QPointF(finalArrowTip.x() - 8 * cos(finalAngle + 0.4),
                            finalArrowTip.y() - 8 * sin(finalAngle + 0.4));
  scene->addPolygon(finalArrowHead, QPen(QColor(70, 130, 180), 2),
                    QBrush(QColor(100, 149, 237)));

  // 结束节点也使用矩形卡片样式
  int endCardWidth = 80;
  int endCardHeight = 50;
  QRectF endCardRect(endPos.x() - endCardWidth / 2, endPos.y(), endCardWidth,
                     endCardHeight);
  QPen endCardPen(Qt::black, 2);
  scene->addRect(endCardRect, endCardPen,
                 QBrush(QColor(255, 182, 193))); // 粉红色背景

  QGraphicsTextItem *endText = scene->addText("结束");
  endText->setFont(QFont("Microsoft YaHei", 10, QFont::Bold));
  endText->setDefaultTextColor(Qt::black);
  QRectF endRect = endText->boundingRect();
  endText->setPos(endPos.x() - endRect.width() / 2,
                  endPos.y() + endCardHeight / 2 - endRect.height() / 2);
}

void MappingWindow::saveMapping() {
  QString fileName = QFileDialog::getSaveFileName(
      this, "保存配置和测试流程",
      QCoreApplication::applicationDirPath() + "/dev_doc", "JSON文件 (*.json)");
  if (fileName.isEmpty())
    return;

  QFile file(fileName);
  if (!file.open(QIODevice::WriteOnly)) {
    QMessageBox::warning(this, "保存失败",
                         "无法打开文件进行写入: " + file.errorString());
    return;
  }

  // 统一使用JSON格式保存
  QJsonObject rootObj;
  QJsonArray mappingArray;

  // 保存映射关系
  for (const MappingRelation &rel : mappingRelations) {
    QJsonObject mapObj;
    mapObj["serialPort"] = rel.serialPort;
    mapObj["device"] = rel.device;
    mapObj["deviceType"] = rel.deviceType;
    mappingArray.append(mapObj);
  }

  rootObj["mapping"] = mappingArray;

  // 保存测试步骤
  QJsonArray testStepsArray;
  for (const TestStep &step : testSteps) {
    QJsonObject stepObj;
    stepObj["type"] = step.type;
    stepObj["device"] = step.device;
    stepObj["command"] = step.command;
    stepObj["expected"] = step.expected;
    stepObj["actual"] = step.actual;
    stepObj["passed"] = step.passed;
    stepObj["notes"] = step.notes;
    stepObj["sendData"] = step.sendData;
    stepObj["expectReceiveData"] = step.expectReceiveData;
    stepObj["actualReceiveData"] = step.actualReceiveData;
    // 添加缺失的字段
    stepObj["stationId"] = step.stationId;
    stepObj["functionCode"] = step.functionCode;
    stepObj["crc16Enabled"] = step.crc16Enabled;
    stepObj["compareEnabled"] = step.compareEnabled; // 保存数据比对复选框状态
    stepObj["targetSerialPort"] = step.targetSerialPort;
    testStepsArray.append(stepObj);
  }
  rootObj["testSteps"] = testStepsArray;

  QJsonDocument doc(rootObj);
  file.write(doc.toJson());
  file.close();
  QMessageBox::information(this, "保存成功",
                           "映射配置和测试流程已保存到: " + fileName);
}

void MappingWindow::loadMapping() {
  QString fileName = QFileDialog::getOpenFileName(
      this, "加载配置和测试流程",
      QCoreApplication::applicationDirPath() + "/dev_doc", "JSON文件 (*.json)");
  if (fileName.isEmpty())
    return;

  QFile file(fileName);
  if (!file.open(QIODevice::ReadOnly)) {
    QMessageBox::warning(this, "加载失败",
                         "无法打开文件进行读取: " + file.errorString());
    return;
  }

  // 清除现有映射和测试步骤
  mappingRelations.clear();
  mapping.clear();
  testSteps.clear();
  undoStack.clear();
  redoStack.clear();

  // 从JSON加载
  QByteArray data = file.readAll();
  QJsonDocument doc = QJsonDocument::fromJson(data);

  if (doc.isNull() || !doc.isObject()) {
    QMessageBox::warning(this, "加载失败", "JSON格式错误");
    return;
  }

  QJsonObject rootObj = doc.object();

  // 加载映射配置
  QJsonArray mappingArray = rootObj["mapping"].toArray();
  for (int i = 0; i < mappingArray.size(); ++i) {
    QJsonObject mapObj = mappingArray[i].toObject();

    // 检查是否为新格式（包含serialPort, device, deviceType）
    if (mapObj.contains("serialPort") && mapObj.contains("device") &&
        mapObj.contains("deviceType")) {
      // 新格式
      QString serialPort = mapObj["serialPort"].toString();
      QString device = mapObj["device"].toString();
      QString deviceType = mapObj["deviceType"].toString();
      mappingRelations.append(MappingRelation(serialPort, device, deviceType));
    } else {
      // 旧格式兼容（controller -> protection）
      QString controller = mapObj["controller"].toString();
      QString protection = mapObj["protection"].toString();
      mapping[controller] = protection;
      // 可以尝试转换为新格式，但需要更多信息来确定serialPort和deviceType
    }
  }

  // 加载测试步骤
  QJsonArray testStepsArray = rootObj["testSteps"].toArray();
  for (int i = 0; i < testStepsArray.size(); ++i) {
    QJsonObject stepObj = testStepsArray[i].toObject();
    TestStep step;
    step.type = stepObj["type"].toString();
    step.device = stepObj["device"].toString();
    step.command = stepObj["command"].toString();
    step.expected = stepObj["expected"].toString();
    step.actual = stepObj["actual"].toString();
    step.passed = stepObj["passed"].toBool();
    step.notes = stepObj["notes"].toString();
    step.sendData = stepObj["sendData"].toString();
    step.expectReceiveData = stepObj["expectReceiveData"].toString();
    step.actualReceiveData = stepObj["actualReceiveData"].toString();
    // 加载新增字段，如果不存在则使用默认值
    step.stationId = stepObj["stationId"].toInt(0);
    step.functionCode = stepObj["functionCode"].toInt(5);
    step.crc16Enabled = stepObj["crc16Enabled"].toBool(false);
    step.compareEnabled =
        stepObj["compareEnabled"].toBool(false); // 加载数据比对复选框状态
    step.targetSerialPort = stepObj["targetSerialPort"].toString("A");
    // 同步赋值
    step.expectReceiveA = stepObj.contains("expectReceiveA")
                              ? stepObj["expectReceiveA"].toString()
                              : step.expectReceiveData;
    if (step.expectReceiveData.isEmpty() && !step.expectReceiveA.isEmpty())
      step.expectReceiveData = step.expectReceiveA;
    if (step.expectReceiveA.isEmpty() && !step.expectReceiveData.isEmpty())
      step.expectReceiveA = step.expectReceiveData;
    testSteps.append(step);
  }

  file.close();

  // 更新UI
  updateMappingList();
  updateGraphicalMapping(-1);
  updateTestStepsList();

  QMessageBox::information(this, "加载成功",
                           "映射配置和测试流程已从文件加载: " + fileName);
}

void MappingWindow::undo() {
  if (undoMappingStack.isEmpty()) {
    QMessageBox::information(this, "撤销", "没有可撤销的操作");
    return;
  }

  // 保存当前状态到重做栈
  redoMappingStack.push(mappingRelations);

  // 恢复到上一个状态
  mappingRelations = undoMappingStack.pop();

  // 更新UI
  updateMappingList();
  updateGraphicalMapping(-1);

  // 自动更新测试流程
  generateTestSteps();

  logMessage("已撤销上一步操作", "INFO");
}

void MappingWindow::redo() {
  if (redoMappingStack.isEmpty()) {
    QMessageBox::information(this, "重做", "没有可重做的操作");
    return;
  }

  // 保存当前状态到撤销栈
  undoMappingStack.push(mappingRelations);

  // 恢复到重做状态
  mappingRelations = redoMappingStack.pop();

  // 更新UI
  updateMappingList();
  updateGraphicalMapping(-1);

  // 自动更新测试流程
  generateTestSteps();

  logMessage("已重做上一步操作", "INFO");
}

void MappingWindow::generateTestSteps() {
  // --- 修复：保存旧的testSteps用于信息复用 ---
  QList<TestStep> oldTestSteps = testSteps;
  auto findOldStep = [&](const QString &serialPort, const QString &device,
                         const QString &type) -> const TestStep * {
    for (const TestStep &s : oldTestSteps) {
      // 只匹配非info步骤
      if (s.device == device && s.type == type) {
        // 进一步用serialPort区分（通过notes里找serialPort）
        if (s.notes.contains(serialPort)) {
          return &s;
        }
      }
    }
    return nullptr;
  };
  // --- END ---

  // 清除现有测试步骤
  testSteps.clear();

  // 添加测试信息步骤
  TestStep infoStep;
  infoStep.type = "info";
  infoStep.device = "";
  infoStep.command = "测试开始";
  infoStep.expected = "";
  infoStep.actual = "";
  infoStep.passed = false;
  infoStep.notes = "自动生成的测试流程";
  // 初始化串口测试相关字段
  infoStep.sendData = "";
  infoStep.expectReceiveData = "";
  infoStep.actualReceiveData = "";
  infoStep.stationId = 221; // 默认站号为DD（221）
  testSteps.append(infoStep);

  // 为每个映射生成测试步骤 - 每个映射只生成一个主要操作步骤
  for (const MappingRelation &rel : mappingRelations) {
    TestStep step;
    bool matched = false;
    if (rel.deviceType == "controller") {
      if (controllerRemoteOnCommands.contains(rel.device)) {
        step.type = "controller_coma";
        step.device = rel.device;
        step.command = controllerRemoteOnCommands[rel.device];
        step.expected = "成功";
        step.notes = QString("%1→控制器%2遥控合操作")
                         .arg(rel.serialPort)
                         .arg(rel.device);
        step.stationId = 221;
        step.functionCode = 5;
        QString hexCommandOn = controllerRemoteOnCommands[rel.device];
        QString stationHexOn =
            QString("%1").arg(step.stationId, 2, 16, QChar('0')).toUpper();
        hexCommandOn.replace("Addr", stationHexOn);
        step.sendData = hexCommandOn;
        step.command =
            QString("%1-%2 遥控合").arg(stationHexOn).arg(rel.device);
        step.expectReceiveData = hexCommandOn;
        step.actualReceiveData = "";
        step.expectReceiveA = step.expectReceiveData;
        matched = true;
      } else if (controllerRemoteOffCommands.contains(rel.device)) {
        step.type = "controller_coma";
        step.device = rel.device;
        step.command = controllerRemoteOffCommands[rel.device];
        step.expected = "成功";
        step.notes = QString("%1→控制器%2遥控分操作")
                         .arg(rel.serialPort)
                         .arg(rel.device);
        step.stationId = 221;
        step.functionCode = 5;
        QString hexCommandOff = controllerRemoteOffCommands[rel.device];
        QString stationHexOff =
            QString("%1").arg(step.stationId, 2, 16, QChar('0')).toUpper();
        hexCommandOff.replace("Addr", stationHexOff);
        step.sendData = hexCommandOff;
        step.command =
            QString("%1-%2 遥控分").arg(stationHexOff).arg(rel.device);
        step.expectReceiveData = hexCommandOff;
        step.actualReceiveData = "";
        step.expectReceiveA = step.expectReceiveData;
        matched = true;
      }
    } else if (rel.deviceType == "protection") {
      if (protectionReadCommands.contains(rel.device)) {
        step.type = "protection_comb";
        step.device = rel.device;
        step.command = protectionReadCommands[rel.device];
        step.expected = "ON";
        step.notes = QString("%1→保护装置%2状态读取")
                         .arg(rel.serialPort)
                         .arg(rel.device);
        step.stationId = 221;
        step.functionCode = 5;
        QString hexCommandRead = protectionReadCommands[rel.device];
        QString stationHexRead =
            QString("%1").arg(step.stationId, 2, 16, QChar('0')).toUpper();
        hexCommandRead.replace("Addr", stationHexRead);
        step.sendData = hexCommandRead;
        step.command =
            QString("%1-%2 读取").arg(stationHexRead).arg(rel.device);
        step.expectReceiveData = hexCommandRead;
        step.actualReceiveData = "";
        step.expectReceiveA = step.expectReceiveData;
        matched = true;
      } else if (protectionRemoteCommands.contains(rel.device)) {
        step.type = "protection_comb";
        step.device = rel.device;
        step.command = protectionRemoteCommands[rel.device];
        step.expected = "成功";
        step.notes = QString("%1→保护装置%2遥控操作")
                         .arg(rel.serialPort)
                         .arg(rel.device);
        step.stationId = 221;
        step.functionCode = 5;
        QString hexCommandRemote = protectionRemoteCommands[rel.device];
        QString stationHexRemote =
            QString("%1").arg(step.stationId, 2, 16, QChar('0')).toUpper();
        hexCommandRemote.replace("Addr", stationHexRemote);
        step.sendData = hexCommandRemote;
        step.command =
            QString("%1-%2 遥控").arg(stationHexRemote).arg(rel.device);
        step.expectReceiveData = hexCommandRemote;
        step.actualReceiveData = "";
        step.expectReceiveA = step.expectReceiveData;
        matched = true;
      }
    }
    // 默认步骤
    if (!matched) {
      if (rel.deviceType == "controller") {
        step.type = "controller_coma";
        step.device = rel.device;
        step.command = "DD 05 00 00 FF 00";
        step.expected = "成功";
        step.notes =
            QString("%1→控制器%2默认操作").arg(rel.serialPort).arg(rel.device);
        step.stationId = 221;
        step.functionCode = 5;
        step.sendData = "DD 05 00 00 FF 00";
        step.expectReceiveData = "DD 05 00 00 FF 00";
        step.actualReceiveData = "";
        step.expectReceiveA = step.expectReceiveData;
      } else if (rel.deviceType == "protection") {
        step.type = "protection_comb";
        step.device = rel.device;
        step.command = "DD 05 00 00 FF 00";
        step.expected = "成功";
        step.notes = QString("%1→保护装置%2默认操作")
                         .arg(rel.serialPort)
                         .arg(rel.device);
        step.stationId = 221;
        step.functionCode = 5;
        step.sendData = "DD 05 00 00 FF 00";
        step.expectReceiveData = "DD 05 00 00 FF 00";
        step.actualReceiveData = "";
        step.expectReceiveA = step.expectReceiveData;
      }
    }
    // --- 复用旧节点详细信息 ---
    const TestStep *old = findOldStep(rel.serialPort, rel.device, step.type);
    if (old) {
      step.expected = old->expected;
      step.actual = old->actual;
      step.passed = old->passed;
      step.notes = old->notes;
      step.sendData = old->sendData;
      step.expectReceiveData = old->expectReceiveData;
      step.actualReceiveData = old->actualReceiveData;
      step.stationId = old->stationId;
      step.functionCode = old->functionCode;
      step.crc16Enabled = old->crc16Enabled;
      step.crc16 = old->crc16;
      step.compareEnabled = old->compareEnabled;
      step.targetSerialPort = old->targetSerialPort;
      step.expectReceiveA = old->expectReceiveA;
      step.expectReceiveB = old->expectReceiveB;
      step.actualReceiveA = old->actualReceiveA;
      step.actualReceiveB = old->actualReceiveB;
    }
    testSteps.append(step);
  }

  // 注释：已移除兼容旧映射关系的重复处理代码，现在只使用mappingRelations

  // 添加测试结束信息步骤
  TestStep endStep;
  endStep.type = "info";
  endStep.device = "";
  endStep.command = "测试结束";
  endStep.expected = "";
  endStep.actual = "";
  endStep.passed = false;
  endStep.notes = "所有测试项目已完成";
  // 初始化串口测试相关字段
  endStep.sendData = "";
  endStep.expectReceiveData = "";
  endStep.actualReceiveData = "";
  endStep.stationId = 221; // 默认站号为DD（221）
  testSteps.append(endStep);

  // 检查全局CRC16设置，并应用到所有测试步骤
  bool globalCrc16Enabled = ui->globalCrc16CheckBox->isChecked();
  if (globalCrc16Enabled) {
    for (auto &step : testSteps) {
      step.crc16Enabled = true;
      step.crc16 = "1";

      // 更新sendData字段，添加CRC16校验码
      if (!step.sendData.isEmpty()) {
        QString hexStr = step.sendData.trimmed();
        // 移除多余空格
        while (hexStr.contains("  "))
          hexStr.replace("  ", " ");
        QStringList hexList = hexStr.split(' ', Qt::SkipEmptyParts);

        // 转换为字节数组计算CRC16
        QByteArray data;
        for (const QString &b : hexList) {
          bool ok;
          int byteValue = b.toInt(&ok, 16);
          if (ok) {
            data.append(static_cast<char>(byteValue));
          }
        }

        if (!data.isEmpty()) {
          // 计算并追加CRC16
          QByteArray crcBytes = CrcUtils::calculateCRC16Bytes(data);
          hexList.append(
              QString("%1")
                  .arg(static_cast<quint8>(crcBytes[0]), 2, 16, QChar('0'))
                  .toUpper());
          hexList.append(
              QString("%1")
                  .arg(static_cast<quint8>(crcBytes[1]), 2, 16, QChar('0'))
                  .toUpper());

          // 更新sendData字段
          step.sendData = hexList.join(' ');

          // 同时更新期望接收数据为与发送数据一致
          step.expectReceiveData = step.sendData;
          step.expectReceiveA = step.sendData;
        }
      }
    }
  }

  // 对于没有启用全局CRC16的步骤，也设置期望接收数据与发送数据一致
  for (auto &step : testSteps) {
    if (!step.crc16Enabled && !step.sendData.isEmpty()) {
      step.expectReceiveData = step.sendData;
      step.expectReceiveA = step.sendData;
    }
  }

  // 更新测试步骤列表
  updateTestStepsList();
}

void MappingWindow::updateTestStepsList() {
  testStepsList->clear();

  for (int i = 0; i < testSteps.size(); ++i) {
    const TestStep &step = testSteps[i];
    QString displayText;

    if (step.type == "info") {
      displayText = QString("%1. [信息] %2").arg(i + 1).arg(step.command);
    } else {
      // 显示sendData而不是command，如果sendData为空则显示command
      QString dataToShow =
          step.sendData.isEmpty() ? step.command : step.sendData;
      QString stepTypeText;

      if (step.type == "controller" || step.type == "controller_coma") {
        stepTypeText = "计算机(COMA)→控制器";
      } else if (step.type == "protection_comb") {
        stepTypeText = "计算机(COMB)→保护装置";
      } else if (step.type == "protection") {
        stepTypeText = "计算机(COMA)→保护装置";
      }

      displayText = QString("%1. [%2] %3 - %4")
                        .arg(i + 1)
                        .arg(stepTypeText)
                        .arg(step.device)
                        .arg(dataToShow);
    }

    // 添加状态标记
    if (step.passed) {
      displayText += " [通过]";
    } else if (!step.actual.isEmpty()) {
      displayText += " [失败]";
    }

    QListWidgetItem *item = new QListWidgetItem(displayText);

    // 设置颜色
    if (step.passed) {
      item->setForeground(Qt::green);
    } else if (!step.actual.isEmpty()) {
      item->setForeground(Qt::red);
    }

    testStepsList->addItem(item);
  }

  // 同步更新图形显示
  updateGraphicalMapping(-1);
}

// 这个函数已被整合到saveMapping中

void MappingWindow::editTestStep() {
  int currentRow = testStepsList->currentRow();
  if (currentRow < 0 || currentRow >= testSteps.size()) {
    QMessageBox::warning(this, "编辑失败", "请先选择一个测试步骤");
    return;
  }

  TestStep &step = testSteps[currentRow];

  // 创建编辑对话框
  QDialog dialog(this);
  dialog.setWindowTitle("编辑测试步骤");
  dialog.setMinimumWidth(500); // 设置最小宽度以适应更多内容

  QVBoxLayout *layout = new QVBoxLayout(&dialog);

  // 步骤类型（下拉菜单）
  QHBoxLayout *typeLayout = new QHBoxLayout;
  typeLayout->addWidget(new QLabel("步骤类型:"));
  QComboBox *typeCombo = new QComboBox;
  typeCombo->addItem("计算机(COMA)→控制器(串口A)", "controller_coma");
  typeCombo->addItem("计算机(COMB)→保护装置(串口B)", "protection_comb");
  typeCombo->addItem("信息", "info");

  // 设置当前值
  int typeIndex = 0;
  if (step.type == "controller" || step.type == "controller_coma")
    typeIndex = 0;
  else if (step.type == "protection" || step.type == "protection_comb")
    typeIndex = 1;
  else if (step.type == "info")
    typeIndex = 2;
  typeCombo->setCurrentIndex(typeIndex);

  typeLayout->addWidget(typeCombo);
  layout->addLayout(typeLayout);

  // 设备站号（下拉菜单）
  QHBoxLayout *stationLayout = new QHBoxLayout;
  stationLayout->addWidget(new QLabel("设备站号:"));
  QComboBox *stationCombo = new QComboBox;

  // 添加站号选项（00-FF）
  for (int i = 0; i <= 255; ++i) {
    stationCombo->addItem(QString("%1").arg(i, 2, 10, QChar('0')), i);
  }

  // 设置当前值，默认为DD（221）
  int defaultStationId =
      (step.stationId > 0) ? step.stationId : 221; // DD = 221
  stationCombo->setCurrentIndex(defaultStationId);

  stationLayout->addWidget(stationCombo);
  layout->addLayout(stationLayout);

  // 功能码（下拉菜单）
  QHBoxLayout *functionCodeLayout = new QHBoxLayout;
  functionCodeLayout->addWidget(new QLabel("功能码:"));
  QComboBox *functionCodeCombo = new QComboBox;

  // 添加Modbus功能码选项
  functionCodeCombo->addItem("01 - 读线圈", 1);
  functionCodeCombo->addItem("02 - 读离散输入", 2);
  functionCodeCombo->addItem("03 - 读保持寄存器", 3);
  functionCodeCombo->addItem("04 - 读输入寄存器", 4);
  functionCodeCombo->addItem("05 - 写单个线圈", 5);
  functionCodeCombo->addItem("06 - 写单个寄存器", 6);
  functionCodeCombo->addItem("15 - 写多个线圈", 15);
  functionCodeCombo->addItem("16 - 写多个寄存器", 16);

  // 设置当前值，默认为05（写单个线圈）
  int defaultFunctionCode =
      (step.functionCode > 0) ? step.functionCode : 5; // 默认功能码05
  int functionCodeIndex = 0;
  for (int i = 0; i < functionCodeCombo->count(); i++) {
    if (functionCodeCombo->itemData(i).toInt() == defaultFunctionCode) {
      functionCodeIndex = i;
      break;
    }
  }
  functionCodeCombo->setCurrentIndex(functionCodeIndex);

  functionCodeLayout->addWidget(functionCodeCombo);
  layout->addLayout(functionCodeLayout);

  // 设备名称（只读）
  QHBoxLayout *deviceLayout = new QHBoxLayout;
  deviceLayout->addWidget(new QLabel("设备名称:"));
  QLineEdit *deviceEdit = new QLineEdit;
  deviceEdit->setText(step.device);
  deviceEdit->setReadOnly(true);
  deviceLayout->addWidget(deviceEdit);
  layout->addLayout(deviceLayout);

  // 移除命令内容栏 - 根据用户要求

  // 预期结果（可编辑）
  QHBoxLayout *expectedLayout = new QHBoxLayout;
  expectedLayout->addWidget(new QLabel("预期结果:"));
  QLineEdit *expectedEdit = new QLineEdit;
  expectedEdit->setText(step.expected);
  expectedLayout->addWidget(expectedEdit);
  layout->addLayout(expectedLayout);

  // 实际结果（可编辑）
  QHBoxLayout *actualLayout = new QHBoxLayout;
  actualLayout->addWidget(new QLabel("实际结果:"));
  QLineEdit *actualEdit = new QLineEdit;
  actualEdit->setText(step.actual);
  actualLayout->addWidget(actualEdit);
  layout->addLayout(actualLayout);

  // 备注信息（可编辑）
  QHBoxLayout *notesLayout = new QHBoxLayout;
  notesLayout->addWidget(new QLabel("备注信息:"));
  QLineEdit *notesEdit = new QLineEdit;
  notesEdit->setText(step.notes);
  notesLayout->addWidget(notesEdit);
  layout->addLayout(notesLayout);

  // 添加串口测试相关字段
  // 发送数据（可编辑）
  QHBoxLayout *sendDataLayout = new QHBoxLayout;
  sendDataLayout->addWidget(new QLabel("发送数据(HEX):"));
  QLineEdit *sendDataEdit = new QLineEdit;
  sendDataEdit->setText(step.sendData);
  sendDataEdit->setPlaceholderText("格式: 01 02 03 04");
  sendDataLayout->addWidget(sendDataEdit);
  layout->addLayout(sendDataLayout);

  // 自动生成发送数据按钮
  QPushButton *generateSendDataButton = new QPushButton("根据设备生成命令");
  layout->addWidget(generateSendDataButton);

  // 新增：CRC16校验和比对判断复选框（同一行）
  QCheckBox *crc16CheckBox = new QCheckBox("CRC16校验");
  QCheckBox *compareCheckBox = new QCheckBox("比对判断");
  // 绑定TestStep字段，记忆每个步骤的状态
  // 检查全局CRC16状态或步骤的crc16Enabled状态
  bool globalCrc16Enabled = ui->globalCrc16CheckBox->isChecked();
  crc16CheckBox->setChecked(globalCrc16Enabled || step.crc16Enabled);
  compareCheckBox->setChecked(step.compareEnabled);
  QHBoxLayout *checkBoxLayout = new QHBoxLayout;
  checkBoxLayout->addWidget(crc16CheckBox);
  checkBoxLayout->addWidget(compareCheckBox);
  layout->addLayout(checkBoxLayout);

  // 连接自动生成按钮的点击事件
  connect(
      generateSendDataButton, &QPushButton::clicked,
      [deviceEdit, stationCombo, functionCodeCombo, typeCombo, sendDataEdit,
       &step, crc16CheckBox]() {
        QString currentDeviceName = deviceEdit->text(); // Get from QLineEdit
        int stationId = stationCombo->currentData().toInt();
        QString stationHex =
            QString("%1").arg(stationId, 2, 16, QChar('0')).toUpper();
        int functionCode =
            functionCodeCombo->currentData().toInt(); // 获取功能码
        QString functionCodeHex = QString("%1")
                                      .arg(functionCode, 2, 16, QChar('0'))
                                      .toUpper(); // 转换为十六进制
        QString currentStepType =
            typeCombo->currentData().toString(); // Get current type

        QString newHexCommand = "";
        QString newDescriptiveCommand = "";
        bool commandSuccessfullyGenerated = false;

        // 根据站号和功能码生成基本命令
        newHexCommand = stationHex + " " +
                        functionCodeHex; // 第一字节是地址，第二字节是功能码

        if (currentStepType == "controller" ||
            currentStepType == "controller_coma") {
          // Try to determine action from notes or existing command description
          QString actionHint = step.notes; // Prefer notes for action type
          if (actionHint.isEmpty()) {
            actionHint = step.command; // Fallback to current command
                                       // description if notes are empty
          }

          // 根据功能码确定操作类型
          QString operationType;
          switch (functionCode) {
          case 1:
            operationType = "读线圈";
            break;
          case 2:
            operationType = "读离散输入";
            break;
          case 3:
            operationType = "读保持寄存器";
            break;
          case 4:
            operationType = "读输入寄存器";
            break;
          case 5:
            operationType = "写单个线圈";
            break;
          case 6:
            operationType = "写单个寄存器";
            break;
          case 15:
            operationType = "写多个线圈";
            break;
          case 16:
            operationType = "写多个寄存器";
            break;
          default:
            operationType = "读取";
            break;
          }

          // 根据功能码添加适当的数据
          if (functionCode == 1 || functionCode == 2 || functionCode == 3 ||
              functionCode == 4) {
            // 读取操作：添加起始地址和数量
            newHexCommand += " 00 00 00 01"; // 起始地址0，读取1个
          } else if (functionCode == 5) {
            // 写单个线圈：添加地址和值（FF 00表示ON，00 00表示OFF）
            newHexCommand += " 00 00 FF 00"; // 地址0，值ON
          } else if (functionCode == 6) {
            // 写单个寄存器：添加地址和值
            newHexCommand += " 00 00 00 01"; // 地址0，值1
          } else if (functionCode == 15) {
            // 写多个线圈：添加起始地址、线圈数量、字节数和值
            newHexCommand +=
                " 00 00 00 01 01 01"; // 起始地址0，1个线圈，1个字节，值01
          } else if (functionCode == 16) {
            // 写多个寄存器：添加起始地址、寄存器数量、字节数和值
            newHexCommand +=
                " 00 00 00 01 02 00 01"; // 起始地址0，1个寄存器，2个字节，值0001
          }

          newDescriptiveCommand = QString("%1-%2 %3")
                                      .arg(currentDeviceName)
                                      .arg(stationHex)
                                      .arg(operationType);
          commandSuccessfullyGenerated = true;
        } // Closing brace for 'if (currentStepType == "controller")'
        else if (currentStepType == "protection" ||
                 currentStepType == "protection_comb") {
          // 保护装置的命令生成逻辑类似于控制器
          // 根据功能码确定操作类型
          QString operationType;
          switch (functionCode) {
          case 1:
            operationType = "读线圈";
            break;
          case 2:
            operationType = "读离散输入";
            break;
          case 3:
            operationType = "读保持寄存器";
            break;
          case 4:
            operationType = "读输入寄存器";
            break;
          case 5:
            operationType = "写单个线圈";
            break;
          case 6:
            operationType = "写单个寄存器";
            break;
          case 15:
            operationType = "写多个线圈";
            break;
          case 16:
            operationType = "写多个寄存器";
            break;
          default:
            operationType = "读取";
            break;
          }

          // 根据功能码添加适当的数据
          if (functionCode == 1 || functionCode == 2 || functionCode == 3 ||
              functionCode == 4) {
            // 读取操作：添加起始地址和数量
            newHexCommand += " 00 00 00 01"; // 起始地址0，读取1个
          } else if (functionCode == 5) {
            // 写单个线圈：添加地址和值（FF 00表示ON，00 00表示OFF）
            newHexCommand += " 00 00 FF 00"; // 地址0，值ON
          } else if (functionCode == 6) {
            // 写单个寄存器：添加地址和值
            newHexCommand += " 00 00 00 01"; // 地址0，值1
          } else if (functionCode == 15) {
            // 写多个线圈：添加起始地址、线圈数量、字节数和值
            newHexCommand +=
                " 00 00 00 01 01 01"; // 起始地址0，1个线圈，1个字节，值01
          } else if (functionCode == 16) {
            // 写多个寄存器：添加起始地址、寄存器数量、字节数和值
            newHexCommand +=
                " 00 00 00 01 02 00 01"; // 起始地址0，1个寄存器，2个字节，值0001
          }

          newDescriptiveCommand = QString("%1-%2 %3")
                                      .arg(currentDeviceName)
                                      .arg(stationHex)
                                      .arg(operationType);
          commandSuccessfullyGenerated = true;
        }

        if (commandSuccessfullyGenerated) {
          // 判断crc16复选框
          if (crc16CheckBox->isChecked()) {
            // 生成命令后自动追加CRC16
            QStringList hexList =
                newHexCommand.trimmed().split(' ', Qt::SkipEmptyParts);
            QByteArray data;
            for (const QString &b : hexList)
              data.append(static_cast<char>(b.toInt(nullptr, 16)));
            QByteArray crc = CrcUtils::calculateCRC16Bytes(data);
            hexList.append(
                QString("%1")
                    .arg(static_cast<quint8>(crc[0]), 2, 16, QChar('0'))
                    .toUpper());
            hexList.append(
                QString("%1")
                    .arg(static_cast<quint8>(crc[1]), 2, 16, QChar('0'))
                    .toUpper());
            sendDataEdit->setText(hexList.join(' '));
          } else {
            sendDataEdit->setText(newHexCommand);
          }
          // 命令内容栏已移除，不再更新描述性命令字段
        } else if (currentStepType == "controller" ||
                   currentStepType == "protection") {
          // If generation failed for types that should have commands, clear or
          // indicate error sendDataEdit->clear(); // Optionally clear
          // commandEdit->setText("无法生成命令"); // Optionally set error text
        }
      });

  // 期望接收数据（可编辑）
  QHBoxLayout *expectReceiveLayout = new QHBoxLayout;
  expectReceiveLayout->addWidget(new QLabel("期望接收数据(HEX):"));
  QLineEdit *expectReceiveEdit = new QLineEdit;
  expectReceiveEdit->setText(step.expectReceiveData);
  expectReceiveEdit->setPlaceholderText("格式: 01 02 03 04");
  expectReceiveLayout->addWidget(expectReceiveEdit);
  layout->addLayout(expectReceiveLayout);

  // 实际接收数据（只读）
  QHBoxLayout *actualReceiveLayout = new QHBoxLayout;
  actualReceiveLayout->addWidget(new QLabel("实际接收数据(HEX):"));
  QLineEdit *actualReceiveEdit = new QLineEdit;
  // 优先显示串口A或串口B的数据
  QString actualDataToShow;
  if (!step.actualReceiveA.isEmpty()) {
    actualDataToShow = step.actualReceiveA;
  } else if (!step.actualReceiveB.isEmpty()) {
    actualDataToShow = step.actualReceiveB;
  } else {
    actualDataToShow = step.actualReceiveData;
  }
  actualReceiveEdit->setText(actualDataToShow);
  actualReceiveEdit->setReadOnly(true);
  actualReceiveLayout->addWidget(actualReceiveEdit);
  layout->addLayout(actualReceiveLayout);

  // 监听CRC16复选框状态变化，自动处理发送数据栏
  connect(crc16CheckBox, &QCheckBox::toggled, [sendDataEdit, crc16CheckBox]() {
    QString hexStr = sendDataEdit->text().trimmed();
    if (hexStr.isEmpty())
      return;
    while (hexStr.contains("  "))
      hexStr.replace("  ", " ");
    QStringList hexList = hexStr.split(' ', Qt::SkipEmptyParts);
    if (crc16CheckBox->isChecked()) {
      // 选中时：直接在当前内容后追加CRC16
      QByteArray data;
      for (const QString &b : hexList)
        data.append(static_cast<char>(b.toInt(nullptr, 16)));
      QByteArray crc = CrcUtils::calculateCRC16Bytes(data);
      hexList.append(QString("%1")
                         .arg(static_cast<quint8>(crc[0]), 2, 16, QChar('0'))
                         .toUpper());
      hexList.append(QString("%1")
                         .arg(static_cast<quint8>(crc[1]), 2, 16, QChar('0'))
                         .toUpper());
      sendDataEdit->setText(hexList.join(' '));
    } else {
      // 取消勾选时，移除末尾2字节
      if (hexList.size() > 2) {
        hexList.removeLast();
        hexList.removeLast();
        sendDataEdit->setText(hexList.join(' '));
      }
    }
  });

  // 按钮
  QHBoxLayout *buttonLayout = new QHBoxLayout;
  QPushButton *okButton = new QPushButton("确定");
  QPushButton *cancelButton = new QPushButton("取消");

  connect(okButton, &QPushButton::clicked, &dialog, &QDialog::accept);
  connect(cancelButton, &QPushButton::clicked, &dialog, &QDialog::reject);

  buttonLayout->addWidget(okButton);
  buttonLayout->addWidget(cancelButton);
  layout->addLayout(buttonLayout);

  // 显示对话框
  if (dialog.exec() == QDialog::Accepted) {
    // 更新测试步骤
    step.type = typeCombo->currentData().toString();
    step.expected = expectedEdit->text();
    step.actual = actualEdit->text();
    step.notes = notesEdit->text();
    step.stationId = stationCombo->currentData().toInt();
    step.functionCode = functionCodeCombo->currentData().toInt();
    // 更新串口测试相关字段
    step.sendData = sendDataEdit->text();
    step.expectReceiveData =
        expectReceiveEdit->text(); // 保存到expectReceiveData字段
    step.expectReceiveA = expectReceiveEdit->text(); // 同时更新新格式字段
    step.actualReceiveData =
        actualReceiveEdit->text(); // 保存到actualReceiveData字段
    step.actualReceiveA = actualReceiveEdit->text(); // 同时更新新格式字段
    // 其它字段保持不变
    step.crc16 = crc16CheckBox->isChecked() ? "1" : "0";
    step.crc16Enabled = crc16CheckBox->isChecked();
    step.compareEnabled = compareCheckBox->isChecked();
    updateTestStepsList();
  }
}

void MappingWindow::showTestStepDetails() {
  int currentRow = testStepsList->currentRow();
  if (currentRow < 0 || currentRow >= testSteps.size()) {
    QMessageBox::warning(this, "查看失败", "请先选择一个测试步骤");
    return;
  }

  const TestStep &step = testSteps[currentRow];

  // 创建详情对话框
  QDialog dialog(this);
  dialog.setWindowTitle("测试步骤详情");
  dialog.setMinimumWidth(500);

  QVBoxLayout *layout = new QVBoxLayout(&dialog);

  // 步骤编号
  QLabel *numberLabel = new QLabel(QString("步骤编号: %1").arg(currentRow + 1));
  layout->addWidget(numberLabel);

  // 步骤类型
  QString typeStr;
  if (step.type == "controller") {
    typeStr = "控制器(串口A)";
  } else if (step.type == "protection") {
    typeStr = "保护装置(串口B)";
  } else {
    typeStr = "信息";
  }
  QLabel *typeLabel = new QLabel(QString("步骤类型: %1").arg(typeStr));
  layout->addWidget(typeLabel);

  // 设备名称
  if (!step.device.isEmpty()) {
    QLabel *deviceLabel = new QLabel(QString("设备名称: %1").arg(step.device));
    layout->addWidget(deviceLabel);
  }

  // 命令内容
  QLabel *commandLabel = new QLabel(QString("命令内容: %1").arg(step.command));
  layout->addWidget(commandLabel);

  // 预期结果
  if (!step.expected.isEmpty()) {
    QLabel *expectedLabel =
        new QLabel(QString("预期结果: %1").arg(step.expected));
    layout->addWidget(expectedLabel);
  }

  // 实际结果
  if (!step.actual.isEmpty()) {
    QLabel *actualLabel = new QLabel(QString("实际结果: %1").arg(step.actual));
    layout->addWidget(actualLabel);
  }

  // 测试状态
  QLabel *statusLabel =
      new QLabel(QString("测试状态: %1").arg(step.passed ? "通过" : "未通过"));
  statusLabel->setStyleSheet(step.passed ? "color: green;" : "color: red;");
  layout->addWidget(statusLabel);

  // 备注信息
  if (!step.notes.isEmpty()) {
    QLabel *notesLabel = new QLabel(QString("备注信息: %1").arg(step.notes));
    notesLabel->setWordWrap(true);
    layout->addWidget(notesLabel);
  }

  // 显示串口测试相关字段
  if (!step.sendData.isEmpty()) {
    QLabel *sendDataLabel =
        new QLabel(QString("发送数据(HEX): %1").arg(step.sendData));
    layout->addWidget(sendDataLabel);
  }

  if (!step.expectReceiveData.isEmpty()) {
    QLabel *expectReceiveLabel = new QLabel(
        QString("期望接收数据(HEX): %1").arg(step.expectReceiveData));
    layout->addWidget(expectReceiveLabel);
  }

  // 显示实际接收数据 - 优先显示串口A或串口B的数据
  QString actualDataToShow;
  QString dataSource;
  if (!step.actualReceiveA.isEmpty()) {
    actualDataToShow = step.actualReceiveA;
    dataSource = "串口A";
  } else if (!step.actualReceiveB.isEmpty()) {
    actualDataToShow = step.actualReceiveB;
    dataSource = "串口B";
  } else if (!step.actualReceiveData.isEmpty()) {
    actualDataToShow = step.actualReceiveData;
    dataSource = "通用";
  }

  if (!actualDataToShow.isEmpty()) {
    QLabel *actualReceiveLabel =
        new QLabel(QString("实际接收数据(HEX): %1 (%2)")
                       .arg(actualDataToShow, dataSource));
    layout->addWidget(actualReceiveLabel);
  }

  // 关闭按钮
  QPushButton *closeButton = new QPushButton("关闭");
  connect(closeButton, &QPushButton::clicked, &dialog, &QDialog::accept);
  layout->addWidget(closeButton, 0, Qt::AlignRight);

  // 显示对话框
  dialog.exec();
}

void MappingWindow::onTestButtonClicked() {
  if (testSteps.isEmpty()) {
    QMessageBox::warning(this, "测试警告",
                         "没有测试步骤可以执行，请先生成测试流程");
    return;
  }

  if (isRunningTests) {
    stopTest();
  } else {
    startTest();
  }
}

void MappingWindow::onClearLogButtonClicked() {
  logTextEdit->clear();
  logMessage("日志已清空", "INFO");
}

void MappingWindow::startTest() {
  // 检查串口连接
  if (!connectToSerialPorts()) {
    QString message = "无法开始测试，串口未连接！\n\n";
    message += "请检查以下项目：\n";
    message += "1. 确认串口A或串口B至少有一个已连接\n";
    message += "2. 检查串口配置是否正确\n";
    message += "3. 确认设备已正确连接到串口\n\n";
    message += "请在主界面连接串口后再开始测试。";
    QMessageBox::warning(this, "测试无法开始", message);
    return;
  }

  // 重置测试状态
  currentTestIndex = 0;
  isRunningTests = true;

  // 清空接收缓冲区
  receivedDataA.clear();
  receivedDataB.clear();

  // 更新按钮状态
  testButton->setText("停止测试");
  testButton->setStyleSheet("QPushButton {"
                            "    background-color: #f44336;"
                            "    color: white;"
                            "    border: none;"
                            "    padding: 8px 16px;"
                            "    border-radius: 4px;"
                            "    font-weight: bold;"
                            "}"
                            "QPushButton:hover {"
                            "    background-color: #da190b;"
                            "}"
                            "QPushButton:pressed {"
                            "    background-color: #c62828;"
                            "}");

  logMessage("开始执行测试流程", "INFO");

  // 启动第一个测试
  runNextTest();
}

void MappingWindow::stopTest() {
  isRunningTests = false;
  testTimer->stop();
  testTimeoutTimer->stop();

  // 更新按钮状态
  testButton->setText("开始测试");
  testButton->setStyleSheet("QPushButton {"
                            "    background-color: #4CAF50;"
                            "    color: white;"
                            "    border: none;"
                            "    padding: 8px 16px;"
                            "    border-radius: 4px;"
                            "    font-weight: bold;"
                            "}"
                            "QPushButton:hover {"
                            "    background-color: #45a049;"
                            "}"
                            "QPushButton:pressed {"
                            "    background-color: #3d8b40;"
                            "}");

  logMessage("测试已停止", "INFO");
}

void MappingWindow::runNextTest() {
  try {
    testTimer->stop();
    if (currentTestIndex >= testSteps.size()) {
      stopTest();
      logMessage("所有测试步骤执行完毕", "INFO");
      QMessageBox::information(this, "测试完成", "所有测试步骤已执行完毕");
      return;
    }
    TestStep &step = testSteps[currentTestIndex];
    logMessage(QString("执行测试步骤 %1: %2")
                   .arg(currentTestIndex + 1)
                   .arg(step.notes),
               "INFO");
    updateFlowchartWithTestProgress(currentTestIndex, false);
    if (step.type == "info") {
      step.passed = true;
      step.actual = "信息步骤";
      logMessage("信息步骤执行完成", "SUCCESS");
    } else {
      ModbusSerialBase *port = nullptr;
      QString sendData;
      QString expectReceive;
      // 按每个step的type决定串口和数据字段
      if (step.type == "controller" || step.type == "controller_coma") {
        port = serialA;
        sendData = !step.sentDataA.isEmpty() ? step.sentDataA : step.sendData;
        expectReceive = step.expectReceiveA;
      } else if (step.type == "protection" || step.type == "protection_comb") {
        port = serialB;
        sendData = !step.sentDataB.isEmpty() ? step.sentDataB : step.sendData;
        expectReceive = step.expectReceiveB;
      }
      if (!port || !port->isConnected()) {
        step.passed = false;
        step.actual = "串口未连接";
        logMessage(QString("串口未连接，无法发送数据到%1")
                       .arg((port == serialA ? "串口A" : "串口B")),
                   "ERROR");

        // 弹出详细的错误提示
        QString message = QString("测试步骤执行失败：%1未连接！\n\n")
                              .arg((port == serialA ? "串口A" : "串口B"));
        message += "请检查以下项目：\n";
        message += QString("1. 确认%1已在主界面连接\n")
                       .arg((port == serialA ? "串口A" : "串口B"));
        message += "2. 检查串口配置是否正确\n";
        message += "3. 确认设备已正确连接到串口\n\n";
        message += "测试将继续执行下一步骤。";
        QMessageBox::warning(this, "测试步骤失败", message);
      } else {
        if (!sendData.isEmpty()) {
          QByteArray data = hexStringToByteArray(sendData);
          if (!data.isEmpty()) {
            // 现在sendData字段已经包含了CRC16（如果启用的话），直接发送
            logMessage(QString("发送数据到%1: %2")
                           .arg((port == serialA ? "COMA" : "COMB"))
                           .arg(sendData),
                       "SEND");
            port->sendRawData(data);
            testTimeoutTimer->start(3000);
            return;
          }
        }
        step.passed = false;
        step.actual = "发送失败";
        logMessage("发送失败：数据为空", "ERROR");
      }
    }
    updateTestStepsList();
    updateFlowchartWithTestProgress(currentTestIndex, step.passed);
    currentTestIndex++;
    int testDelay = ui->testDelaySpinBox->value();
    testTimer->start(testDelay);
  } catch (const std::exception &e) {
    logMessage(QString("测试执行异常: %1").arg(e.what()), "ERROR");
    currentTestIndex++;
    testTimer->start(1000);
  }
}

void MappingWindow::onTestTimeout() {
  testTimeoutTimer->stop();

  if (currentTestIndex < testSteps.size()) {
    TestStep &step = testSteps[currentTestIndex];
    step.passed = false;
    step.actual = "超时";

    logMessage(QString("测试步骤 %1 超时").arg(currentTestIndex + 1), "ERROR");

    // 更新测试步骤列表
    updateTestStepsList();

    // 更新流程图
    updateFlowchartWithTestProgress(currentTestIndex, false);

    // 继续下一个测试
    currentTestIndex++;
    int testDelay = ui->testDelaySpinBox->value();
    testTimer->start(testDelay);
  }
}

void MappingWindow::handleSerialAMessageReceived(const QString &message) {
  receivedDataA = message;
  logMessage(QString("串口A接收: %1").arg(message), "RECEIVE");
  if (isRunningTests && currentTestIndex < testSteps.size()) {
    TestStep &step = testSteps[currentTestIndex];
    if (step.targetSerialPort == "A") {
      testTimeoutTimer->stop();
      bool passed = false;
      // 自动去除RX:前缀
      QString actual = message.trimmed();
      if (actual.startsWith("RX:"))
        actual = actual.mid(3).trimmed();
      step.actualReceiveA = actual.toUpper();
      step.actualReceiveData =
          actual.toUpper(); // 同时更新actualReceiveData字段
      // --- 修正比对逻辑 ---
      auto normalizeHex = [](const QString &s) {
        return s.trimmed().toUpper().replace(QRegularExpression("\\s+"), " ");
      };
      QString expect = normalizeHex(step.expectReceiveA);
      QString actualNorm = normalizeHex(step.actualReceiveA);
      if (step.compareEnabled) {
        passed = (!expect.isEmpty() && actualNorm == expect);
      } else {
        passed = true;
      }
      step.passed = passed;
      step.actual = passed ? "通过" : "失败";
      if (!passed && step.compareEnabled) {
        logMessage(
            QString("比对失败：期望[%1] 实际[%2]").arg(expect, actualNorm),
            "ERROR");
      }
      logMessage(QString("测试步骤 %1 %2")
                     .arg(currentTestIndex + 1)
                     .arg(passed ? "通过" : "失败"),
                 passed ? "SUCCESS" : "ERROR");
      updateTestStepsList();
      updateFlowchartWithTestProgress(currentTestIndex, passed);
      currentTestIndex++;
      int testDelay = ui->testDelaySpinBox->value();
      testTimer->start(testDelay);
    }
  }
}

void MappingWindow::handleSerialBMessageReceived(const QString &message) {
  receivedDataB = message;
  logMessage(QString("串口B接收: %1").arg(message), "RECEIVE");
  if (isRunningTests && currentTestIndex < testSteps.size()) {
    TestStep &step = testSteps[currentTestIndex];
    if (step.targetSerialPort == "B") {
      testTimeoutTimer->stop();
      bool passed = false;
      // 自动去除RX:前缀
      QString actual = message.trimmed();
      if (actual.startsWith("RX:"))
        actual = actual.mid(3).trimmed();
      step.actualReceiveB = actual.toUpper();
      step.actualReceiveData =
          actual.toUpper(); // 同时更新actualReceiveData字段
      // --- 修正比对逻辑 ---
      auto normalizeHex = [](const QString &s) {
        return s.trimmed().toUpper().replace(QRegularExpression("\\s+"), " ");
      };
      QString expect = normalizeHex(step.expectReceiveB);
      QString actualNorm = normalizeHex(step.actualReceiveB);
      if (step.compareEnabled) {
        passed = (!expect.isEmpty() && actualNorm == expect);
      } else {
        passed = true;
      }
      step.passed = passed;
      step.actual = passed ? "通过" : "失败";
      if (!passed && step.compareEnabled) {
        logMessage(
            QString("比对失败：期望[%1] 实际[%2]").arg(expect, actualNorm),
            "ERROR");
      }
      logMessage(QString("测试步骤 %1 %2")
                     .arg(currentTestIndex + 1)
                     .arg(passed ? "通过" : "失败"),
                 passed ? "SUCCESS" : "ERROR");
      updateTestStepsList();
      updateFlowchartWithTestProgress(currentTestIndex, passed);
      currentTestIndex++;
      int testDelay = ui->testDelaySpinBox->value();
      testTimer->start(testDelay);
    }
  }
}

void MappingWindow::logMessage(const QString &message, const QString &type) {
  QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
  QString logEntry = QString("[%1] [%2] %3").arg(timestamp, type, message);

  // 根据类型设置颜色
  QString color;
  if (type == "ERROR") {
    color = "red";
  } else if (type == "SUCCESS") {
    color = "green";
  } else if (type == "SEND") {
    color = "blue";
  } else if (type == "RECEIVE") {
    color = "purple";
  } else {
    color = "black";
  }

  logTextEdit->append(
      QString("<span style='color: %1;'>%2</span>").arg(color, logEntry));

  // 滚动到底部
  QTextCursor cursor = logTextEdit->textCursor();
  cursor.movePosition(QTextCursor::End);
  logTextEdit->setTextCursor(cursor);
}

void MappingWindow::updateFlowchartWithTestProgress(int currentStep,
                                                    bool passed) {
  // 清除之前的流程图项
  for (QGraphicsItem *item : flowchartItems) {
    scene->removeItem(item);
    delete item;
  }
  flowchartItems.clear();

  // 重新绘制流程图，并根据测试进度更新颜色
  updateGraphicalMapping(currentStep);

  // 如果有当前测试步骤，高亮显示
  // 已在updateGraphicalMapping中处理
}

QByteArray MappingWindow::hexStringToByteArray(const QString &hexString) {
  QByteArray result;
  QString cleanHex = hexString.simplified().remove(" ");

  for (int i = 0; i < cleanHex.length(); i += 2) {
    QString byteString = cleanHex.mid(i, 2);
    bool ok;
    char byte = static_cast<char>(byteString.toInt(&ok, 16));
    if (ok) {
      result.append(byte);
    }
  }

  return result;
}

QString MappingWindow::byteArrayToHexString(const QByteArray &data) {
  QString result;
  for (char byte : data) {
    result += QString("%1 ")
                  .arg(static_cast<quint8>(byte) & 0xFF, 2, 16, QChar('0'))
                  .toUpper();
  }
  return result.trimmed();
}

bool MappingWindow::connectToSerialPorts() {
  // 检查串口对象是否存在并已连接
  bool serialAConnected = (serialA && serialA->isConnected());
  bool serialBConnected = (serialB && serialB->isConnected());

  // 如果至少有一个串口连接，就认为连接成功
  // 因为测试可能只需要使用其中一个串口
  if (serialAConnected || serialBConnected) {
    return true;
  }

  // 如果都没有连接，返回false
  return false;
}

void MappingWindow::disconnectFromSerialPorts() {
  // 断开串口连接
  if (serialA) {
    serialA->disconnectDevice();
  }
  if (serialB) {
    serialB->disconnectDevice();
  }
}

void MappingWindow::setSerialPorts(ModbusSerialA *portA, ModbusSerialB *portB) {
  serialA = portA;
  serialB = portB;
  // 连接信号
  if (serialA)
    connect(serialA, &ModbusSerialA::communicationMessageReceived, this,
            &MappingWindow::handleSerialAMessageReceived);
  if (serialB)
    connect(serialB, &ModbusSerialB::communicationMessageReceived, this,
            &MappingWindow::handleSerialBMessageReceived);
}

void MappingWindow::onTestReportButtonClicked() { showTestReport(); }

void MappingWindow::onMappingListContextMenuRequested(const QPoint &pos) {
  QListWidgetItem *item = mappingList->itemAt(pos);
  QMenu menu(this);
  QAction *insertAction = menu.addAction("插入新映射");
  QAction *deleteAction = nullptr;
  if (item) {
    deleteAction = menu.addAction("删除该映射");
  }
  QAction *selectedAction =
      menu.exec(mappingList->viewport()->mapToGlobal(pos));
  if (selectedAction == insertAction) {
    onInsertMappingAt();
  } else if (deleteAction && selectedAction == deleteAction) {
    onDeleteMappingAt();
  }
}

void MappingWindow::onInsertMappingAt() {
  int row = mappingList->currentRow();
  if (row < 0)
    row = mappingRelations.size();
  // 弹窗选择串口和设备
  QDialog dialog(this);
  dialog.setWindowTitle("插入新映射");
  QVBoxLayout *layout = new QVBoxLayout(&dialog);
  QComboBox *portCombo = new QComboBox(&dialog);
  portCombo->addItem("COMA");
  portCombo->addItem("COMB");
  QComboBox *deviceCombo = new QComboBox(&dialog);
  // 默认显示控制器设备
  deviceCombo->addItems(controllerDevices);
  connect(portCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
          [&](int idx) {
            deviceCombo->clear();
            if (idx == 0)
              deviceCombo->addItems(controllerDevices);
            else
              deviceCombo->addItems(protectionDevices);
          });
  layout->addWidget(new QLabel("串口："));
  layout->addWidget(portCombo);
  layout->addWidget(new QLabel("设备："));
  layout->addWidget(deviceCombo);
  QDialogButtonBox *btnBox = new QDialogButtonBox(
      QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &dialog);
  layout->addWidget(btnBox);
  connect(btnBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
  connect(btnBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);
  if (dialog.exec() == QDialog::Accepted) {
    QString serialPort = portCombo->currentText();
    QString device = deviceCombo->currentText();
    QString deviceType =
        (portCombo->currentIndex() == 0) ? "controller" : "protection";
    // 检查是否已存在
    for (const auto &rel : mappingRelations) {
      if (rel.serialPort == serialPort && rel.device == device) {
        QMessageBox::warning(this, "重复映射", "该映射已存在！");
        return;
      }
    }
    // 保存当前状态到撤销栈
    QList<MappingRelation> oldMappingRelations = mappingRelations;
    mappingRelations.insert(row,
                            MappingRelation(serialPort, device, deviceType));
    undoMappingStack.push(oldMappingRelations);
    redoMappingStack.clear();
    updateMappingList();
    updateGraphicalMapping(-1);
    generateTestSteps();
  }
}

void MappingWindow::onDeleteMappingAt() {
  int row = mappingList->currentRow();
  if (row < 0 || row >= mappingRelations.size())
    return;
  int ret = QMessageBox::question(this, "确认删除", "确定要删除该映射吗？",
                                  QMessageBox::Yes | QMessageBox::No);
  if (ret == QMessageBox::Yes) {
    QList<MappingRelation> oldMappingRelations = mappingRelations;
    mappingRelations.removeAt(row);
    undoMappingStack.push(oldMappingRelations);
    redoMappingStack.clear();
    updateMappingList();
    updateGraphicalMapping(-1);
    generateTestSteps();
  }
}

// 全局CRC16校验复选框状态变化处理
void MappingWindow::onGlobalCrc16CheckBoxToggled(bool checked) {
  // 更新所有现有测试步骤的CRC16校验状态
  for (auto &step : testSteps) {
    step.crc16Enabled = checked;
    step.crc16 = checked ? "1" : "0";

    // 更新sendData字段
    if (!step.sendData.isEmpty()) {
      QString hexStr = step.sendData.trimmed();
      // 移除多余空格
      while (hexStr.contains("  "))
        hexStr.replace("  ", " ");
      QStringList hexList = hexStr.split(' ', Qt::SkipEmptyParts);

      if (checked) {
        // 启用CRC16：检查是否已经包含CRC16，如果没有则添加
        // 简单检查：如果数据长度是偶数且大于6字节，可能已经包含CRC16
        if (hexList.size() <= 6 || hexList.size() % 2 != 0) {
          // 转换为字节数组计算CRC16
          QByteArray data;
          for (const QString &b : hexList) {
            bool ok;
            int byteValue = b.toInt(&ok, 16);
            if (ok) {
              data.append(static_cast<char>(byteValue));
            }
          }

          if (!data.isEmpty()) {
            // 计算并追加CRC16
            QByteArray crcBytes = CrcUtils::calculateCRC16Bytes(data);
            hexList.append(
                QString("%1")
                    .arg(static_cast<quint8>(crcBytes[0]), 2, 16, QChar('0'))
                    .toUpper());
            hexList.append(
                QString("%1")
                    .arg(static_cast<quint8>(crcBytes[1]), 2, 16, QChar('0'))
                    .toUpper());

            // 更新sendData字段
            step.sendData = hexList.join(' ');

            // 同时更新期望接收数据
            step.expectReceiveData = step.sendData;
            step.expectReceiveA = step.sendData;
          }
        }
      } else {
        // 禁用CRC16：移除最后两个字节（如果存在）
        if (hexList.size() > 6) {
          hexList.removeLast(); // 移除CRC16高字节
          hexList.removeLast(); // 移除CRC16低字节
          step.sendData = hexList.join(' ');

          // 同时更新期望接收数据
          step.expectReceiveData = step.sendData;
          step.expectReceiveA = step.sendData;
        }
      }
    }
  }

  // 更新测试步骤列表显示
  updateTestStepsList();

  qDebug() << "全局CRC16校验状态已更改为:" << (checked ? "启用" : "禁用");
}

// 复位测试状态到映射配置刚添加时的状态
void MappingWindow::resetTestState() {
  // 1. 重置所有测试步骤的状态
  for (auto &step : testSteps) {
    step.passed = false;         // 重置通过状态
    step.actual = "";            // 清空实际结果（这个字段决定颜色显示）
    step.actualReceiveData = ""; // 清空实际接收数据
    step.actualReceiveA = "";    // 清空实际接收数据A
    step.actualReceiveB = "";    // 清空实际接收数据B
  }

  // 2. 重置测试相关的状态变量
  currentTestIndex = 0;   // 重置当前测试索引
  isRunningTests = false; // 重置测试运行状态
  selectedNodeIndex = -1; // 重置选中的节点索引（取消选中状态）

  // 3. 停止所有定时器
  if (testTimeoutTimer) {
    testTimeoutTimer->stop();
  }
  if (testTimer) {
    testTimer->stop();
  }

  // 4. 更新测试按钮状态
  if (testButton) {
    testButton->setText("开始测试");
    testButton->setEnabled(true);
  }

  // 5. 更新测试步骤列表显示（清除颜色标记）
  updateTestStepsList();

  // 6. 更新图形化映射显示（清除对勾和叉标记）
  updateGraphicalMapping(-1);

  // 7. 显示状态信息
  QMessageBox::information(this, "复位完成",
                           "测试状态已复位到映射配置刚添加时的状态。\n所有测试"
                           "结果已清除，可以重新开始测试。");

  qDebug() << "测试状态已复位，共复位" << testSteps.size() << "个测试步骤";
}

// 显示设备下拉框右键菜单
void MappingWindow::showDeviceContextMenu(const QPoint &pos) {
  // 检查是否有设备项
  if (deviceComboBox->count() == 0) {
    return;
  }

  QMenu contextMenu(this);
  QAction *editAction = contextMenu.addAction("编辑设备名称");
  editAction->setIcon(QIcon(":/icons/settings.svg"));

  QAction *selectedAction = contextMenu.exec(deviceComboBox->mapToGlobal(pos));
  if (selectedAction == editAction) {
    editDeviceName();
  }
}

// 编辑设备名称
void MappingWindow::editDeviceName() {
  int currentIndex = deviceComboBox->currentIndex();
  if (currentIndex < 0) {
    QMessageBox::warning(this, "编辑失败", "请先选择一个设备");
    return;
  }

  QString currentDevice = deviceComboBox->currentText();
  bool ok;
  QString newDeviceName = QInputDialog::getText(
      this, "编辑设备名称", "请输入新的设备名称:", QLineEdit::Normal,
      currentDevice, &ok);

  if (ok && !newDeviceName.trimmed().isEmpty() &&
      newDeviceName.trimmed() != currentDevice) {
    newDeviceName = newDeviceName.trimmed();

    // 检查新名称是否已存在
    bool nameExists = false;
    for (int i = 0; i < deviceComboBox->count(); ++i) {
      if (i != currentIndex && deviceComboBox->itemText(i) == newDeviceName) {
        nameExists = true;
        break;
      }
    }

    if (nameExists) {
      QMessageBox::warning(this, "编辑失败", "设备名称已存在，请使用其他名称");
      return;
    }

    // 更新设备列表
    updateDeviceNameInLists(currentDevice, newDeviceName);

    // 更新下拉框
    deviceComboBox->setItemText(currentIndex, newDeviceName);

    // 保存设备列表到文件
    saveDeviceListsToFile();

    QMessageBox::information(this, "编辑成功",
                             QString("设备名称已从 '%1' 更改为 '%2'")
                                 .arg(currentDevice, newDeviceName));
  }
}

// 更新设备列表中的设备名称
void MappingWindow::updateDeviceNameInLists(const QString &oldName,
                                            const QString &newName) {
  // 更新控制器设备列表
  int index = controllerDevices.indexOf(oldName);
  if (index >= 0) {
    controllerDevices[index] = newName;
  }

  // 更新保护装置列表
  index = protectionDevices.indexOf(oldName);
  if (index >= 0) {
    protectionDevices[index] = newName;
  }

  // 更新映射关系中的设备名称
  for (auto &relation : mappingRelations) {
    if (relation.device == oldName) {
      relation.device = newName;
    }
  }

  // 更新测试步骤中的设备名称
  for (auto &step : testSteps) {
    if (step.device == oldName) {
      step.device = newName;
    }
  }

  // 更新界面显示
  updateMappingList();
  updateTestStepsList();
}

// 保存设备列表到文件
void MappingWindow::saveDeviceListsToFile() {
  QString fileName =
      QCoreApplication::applicationDirPath() + "/dev_doc/device_lists.json";
  QFile file(fileName);

  if (!file.open(QIODevice::WriteOnly)) {
    qDebug() << "无法打开文件进行写入:" << fileName;
    return;
  }

  QJsonObject rootObject;

  // 保存控制器设备列表
  QJsonArray controllerArray;
  for (const QString &device : controllerDevices) {
    controllerArray.append(device);
  }
  rootObject["controllerDevices"] = controllerArray;

  // 保存保护装置列表
  QJsonArray protectionArray;
  for (const QString &device : protectionDevices) {
    protectionArray.append(device);
  }
  rootObject["protectionDevices"] = protectionArray;

  QJsonDocument doc(rootObject);
  file.write(doc.toJson());
  file.close();

  qDebug() << "设备列表已保存到文件:" << fileName;
}

// 从文件加载设备列表
void MappingWindow::loadDeviceListsFromFile() {
  QString fileName =
      QCoreApplication::applicationDirPath() + "/dev_doc/device_lists.json";
  QFile file(fileName);

  if (!file.open(QIODevice::ReadOnly)) {
    qDebug() << "设备列表文件不存在，使用默认设备列表:" << fileName;
    return;
  }

  QByteArray data = file.readAll();
  file.close();

  QJsonDocument doc = QJsonDocument::fromJson(data);
  if (!doc.isObject()) {
    qDebug() << "设备列表文件格式错误";
    return;
  }

  QJsonObject rootObject = doc.object();

  // 加载控制器设备列表
  if (rootObject.contains("controllerDevices") &&
      rootObject["controllerDevices"].isArray()) {
    QJsonArray controllerArray = rootObject["controllerDevices"].toArray();
    controllerDevices.clear();
    for (const QJsonValue &value : controllerArray) {
      if (value.isString()) {
        controllerDevices.append(value.toString());
      }
    }
  }

  // 加载保护装置列表
  if (rootObject.contains("protectionDevices") &&
      rootObject["protectionDevices"].isArray()) {
    QJsonArray protectionArray = rootObject["protectionDevices"].toArray();
    protectionDevices.clear();
    for (const QJsonValue &value : protectionArray) {
      if (value.isString()) {
        protectionDevices.append(value.toString());
      }
    }
  }

  qDebug() << "设备列表已从文件加载:" << fileName;
  qDebug() << "控制器设备:" << controllerDevices.size() << "个";
  qDebug() << "保护装置:" << protectionDevices.size() << "个";
}

// 测试流程列表双击事件处理
void MappingWindow::onTestStepsListDoubleClicked(QListWidgetItem *item) {
  if (!item)
    return;

  // 获取双击的项目索引
  int index = testStepsList->row(item);
  if (index < 0 || index >= testSteps.size()) {
    return;
  }

  // 设置当前选中项
  testStepsList->setCurrentRow(index);

  // 调用编辑测试步骤函数
  editTestStepByIndex(index);
}

// 流程图双击事件处理
void MappingWindow::onGraphicsViewDoubleClicked(const QPointF &position) {
  // 查找双击位置对应的测试步骤
  QGraphicsItem *item = scene->itemAt(position, QTransform());
  if (!item) {
    return;
  }

  // 检查是否是测试步骤相关的图形项
  QVariant stepIndexVariant = item->data(0);
  if (!stepIndexVariant.isValid()) {
    return;
  }

  int stepIndex = stepIndexVariant.toInt();
  if (stepIndex < 0 || stepIndex >= testSteps.size()) {
    return;
  }

  // 同步选中测试流程列表中的对应项
  testStepsList->setCurrentRow(stepIndex);

  // 调用编辑测试步骤函数
  editTestStepByIndex(stepIndex);
}

// 根据索引编辑测试步骤（设备名称可编辑）
void MappingWindow::editTestStepByIndex(int index) {
  if (index < 0 || index >= testSteps.size()) {
    QMessageBox::warning(this, "编辑失败", "无效的测试步骤索引");
    return;
  }

  TestStep &step = testSteps[index];

  QDialog dialog(this);
  dialog.setWindowTitle(QString("编辑测试步骤 %1").arg(index + 1));
  dialog.setModal(true);
  dialog.resize(500, 450); // 缩小对话框尺寸

  QVBoxLayout *mainLayout = new QVBoxLayout(&dialog);
  mainLayout->setSpacing(8);                      // 减小间距
  mainLayout->setContentsMargins(10, 10, 10, 10); // 减小边距

  // 创建网格布局，使界面更紧凑
  QGridLayout *gridLayout = new QGridLayout;
  gridLayout->setSpacing(6);

  // 第一行：测试类型和设备名称
  gridLayout->addWidget(new QLabel("测试类型:"), 0, 0);
  QComboBox *typeCombo = new QComboBox;

  // 根据当前步骤的类型确定可选项，保持串口信息
  if (step.type.contains("controller") || step.type.contains("coma")) {
    typeCombo->addItem("控制器遥控合", "controller_coma");
    typeCombo->addItem("控制器遥控分", "controller_coma");
    typeCombo->addItem("控制器读取", "controller_coma");
  } else if (step.type.contains("protection") || step.type.contains("comb")) {
    typeCombo->addItem("保护装置遥控", "protection_comb");
    typeCombo->addItem("保护装置读取", "protection_comb");
  } else if (step.type == "info") {
    typeCombo->addItem("信息步骤", "info");
  } else {
    // 默认选项，保持原有类型
    typeCombo->addItem("遥控合", step.type);
    typeCombo->addItem("遥控分", step.type);
    typeCombo->addItem("读取", step.type);
    typeCombo->addItem("遥控", step.type);
  }

  // 设置当前值 - 默认选择第一项
  typeCombo->setCurrentIndex(0);
  gridLayout->addWidget(typeCombo, 0, 1);

  gridLayout->addWidget(new QLabel("设备名称:"), 0, 2);
  QLineEdit *deviceEdit = new QLineEdit;
  deviceEdit->setText(step.device);
  deviceEdit->setPlaceholderText("如：JT01、DI01");
  gridLayout->addWidget(deviceEdit, 0, 3);

  // 从发送数据中解析设备站号和功能码
  int parsedStationId = 0;    // 默认00
  int parsedFunctionCode = 5; // 默认05

  if (!step.sendData.isEmpty()) {
    QStringList hexParts =
        step.sendData.trimmed().split(' ', Qt::SkipEmptyParts);
    if (hexParts.size() >= 2) {
      // 第一个字节是设备站号
      bool ok1;
      parsedStationId = hexParts[0].toInt(&ok1, 16);
      if (!ok1)
        parsedStationId = 0;

      // 第二个字节是功能码
      bool ok2;
      parsedFunctionCode = hexParts[1].toInt(&ok2, 16);
      if (!ok2)
        parsedFunctionCode = 5;
    }
  }

  // 第二行：设备站号和功能码
  gridLayout->addWidget(new QLabel("设备站号:"), 1, 0);
  QComboBox *stationCombo = new QComboBox;
  stationCombo->setMaxVisibleItems(10); // 限制下拉框显示项数
  for (int i = 0; i <= 255; ++i) {
    stationCombo->addItem(QString("%1").arg(i, 2, 16, QChar('0')).toUpper(), i);
  }
  // 使用解析出的站号，如果解析失败则使用step中保存的值
  stationCombo->setCurrentIndex(parsedStationId);
  gridLayout->addWidget(stationCombo, 1, 1);

  gridLayout->addWidget(new QLabel("功能码:"), 1, 2);
  QComboBox *functionCodeCombo = new QComboBox;
  functionCodeCombo->addItem("05", 5);
  functionCodeCombo->addItem("06", 6);
  functionCodeCombo->addItem("03", 3);
  functionCodeCombo->addItem("01", 1);
  functionCodeCombo->addItem("02", 2);
  functionCodeCombo->addItem("04", 4);
  // 设置当前值 - 使用解析出的功能码
  for (int i = 0; i < functionCodeCombo->count(); ++i) {
    if (functionCodeCombo->itemData(i).toInt() == parsedFunctionCode) {
      functionCodeCombo->setCurrentIndex(i);
      break;
    }
  }
  gridLayout->addWidget(functionCodeCombo, 1, 3);

  mainLayout->addLayout(gridLayout);

  // 数据区域 - 使用分组框
  QGroupBox *dataGroup = new QGroupBox("数据配置");
  QVBoxLayout *dataLayout = new QVBoxLayout(dataGroup);
  dataLayout->setSpacing(4);

  // 发送数据
  QHBoxLayout *sendLayout = new QHBoxLayout;
  sendLayout->addWidget(new QLabel("发送数据:"));
  QLineEdit *sendDataEdit = new QLineEdit;
  sendDataEdit->setText(step.sendData);
  sendDataEdit->setPlaceholderText("如: DD 05 00 00 FF 00");
  sendLayout->addWidget(sendDataEdit);
  dataLayout->addLayout(sendLayout);

  // 当发送数据改变时，自动更新设备站号和功能码
  connect(
      sendDataEdit, &QLineEdit::textChanged,
      [stationCombo, functionCodeCombo](const QString &text) {
        if (!text.isEmpty()) {
          QStringList hexParts = text.trimmed().split(' ', Qt::SkipEmptyParts);
          if (hexParts.size() >= 2) {
            // 更新设备站号
            bool ok1;
            int stationId = hexParts[0].toInt(&ok1, 16);
            if (ok1 && stationId >= 0 && stationId <= 255) {
              stationCombo->setCurrentIndex(stationId);
            }

            // 更新功能码
            bool ok2;
            int functionCode = hexParts[1].toInt(&ok2, 16);
            if (ok2) {
              for (int i = 0; i < functionCodeCombo->count(); ++i) {
                if (functionCodeCombo->itemData(i).toInt() == functionCode) {
                  functionCodeCombo->setCurrentIndex(i);
                  break;
                }
              }
            }
          }
        }
      });

  // 期望接收数据
  QHBoxLayout *expectLayout = new QHBoxLayout;
  expectLayout->addWidget(new QLabel("期望数据:"));
  QLineEdit *expectReceiveEdit = new QLineEdit;
  expectReceiveEdit->setText(step.expectReceiveData);
  expectReceiveEdit->setPlaceholderText("如: DD 05 00 00 FF 00");
  expectLayout->addWidget(expectReceiveEdit);
  dataLayout->addLayout(expectLayout);

  QHBoxLayout *actualReceiveLayout = new QHBoxLayout;
  actualReceiveLayout->addWidget(new QLabel("实际数据:"));
  QLineEdit *actualReceiveEdit = new QLineEdit;
  // 优先显示串口A或串口B的数据
  QString actualDataToShow;
  if (!step.actualReceiveA.isEmpty()) {
    actualDataToShow = step.actualReceiveA;
  } else if (!step.actualReceiveB.isEmpty()) {
    actualDataToShow = step.actualReceiveB;
  } else {
    actualDataToShow = step.actualReceiveData;
  }
  actualReceiveEdit->setText(actualDataToShow);
  actualReceiveEdit->setPlaceholderText("测试时自动填充");
  actualReceiveLayout->addWidget(actualReceiveEdit);
  dataLayout->addLayout(actualReceiveLayout);

  mainLayout->addWidget(dataGroup);

  // 选项区域 - 使用水平布局
  QHBoxLayout *optionsLayout = new QHBoxLayout;
  QCheckBox *crc16CheckBox = new QCheckBox("CRC16校验");
  crc16CheckBox->setChecked(step.crc16Enabled);
  optionsLayout->addWidget(crc16CheckBox);

  QCheckBox *compareCheckBox = new QCheckBox("数据比对");
  compareCheckBox->setChecked(step.compareEnabled);
  optionsLayout->addWidget(compareCheckBox);
  optionsLayout->addStretch(); // 添加弹性空间

  mainLayout->addLayout(optionsLayout);

  // 结果和备注区域 - 使用网格布局
  QGridLayout *resultLayout = new QGridLayout;
  resultLayout->setSpacing(4);

  resultLayout->addWidget(new QLabel("期望结果:"), 0, 0);
  QLineEdit *expectedEdit = new QLineEdit;
  expectedEdit->setText(step.expected);
  resultLayout->addWidget(expectedEdit, 0, 1);

  resultLayout->addWidget(new QLabel("实际结果:"), 0, 2);
  QLineEdit *actualEdit = new QLineEdit;
  actualEdit->setText(step.actual);
  resultLayout->addWidget(actualEdit, 0, 3);

  resultLayout->addWidget(new QLabel("备注:"), 1, 0);
  QLineEdit *notesEdit = new QLineEdit;
  notesEdit->setText(step.notes);
  resultLayout->addWidget(notesEdit, 1, 1, 1, 3); // 跨3列

  mainLayout->addLayout(resultLayout);

  // 按钮区域
  QHBoxLayout *buttonLayout = new QHBoxLayout;
  buttonLayout->addStretch();
  QPushButton *okButton = new QPushButton("确定");
  QPushButton *cancelButton = new QPushButton("取消");
  okButton->setMinimumWidth(80);
  cancelButton->setMinimumWidth(80);
  buttonLayout->addWidget(okButton);
  buttonLayout->addWidget(cancelButton);
  mainLayout->addLayout(buttonLayout);

  connect(okButton, &QPushButton::clicked, &dialog, &QDialog::accept);
  connect(cancelButton, &QPushButton::clicked, &dialog, &QDialog::reject);

  if (dialog.exec() == QDialog::Accepted) {
    // 保存原设备名称，用于检查是否有变化
    QString oldDeviceName = step.device;

    // 更新测试步骤 - 保持原有的串口类型信息
    // 不更新step.type，保持原有的串口信息（如controller_coma,
    // protection_comb等）
    step.device = deviceEdit->text().trimmed(); // 更新设备名称
    step.expected = expectedEdit->text();
    step.actual = actualEdit->text();
    step.notes = notesEdit->text();
    step.stationId = stationCombo->currentData().toInt();
    step.functionCode = functionCodeCombo->currentData().toInt();
    // 更新串口测试相关字段
    step.sendData = sendDataEdit->text();
    step.expectReceiveData =
        expectReceiveEdit->text(); // 保存到expectReceiveData字段
    step.expectReceiveA = expectReceiveEdit->text(); // 同时更新新格式字段
    step.actualReceiveData =
        actualReceiveEdit->text(); // 保存到actualReceiveData字段
    step.actualReceiveA = actualReceiveEdit->text(); // 同时更新新格式字段
    // 其它字段保持不变
    step.crc16 = crc16CheckBox->isChecked() ? "1" : "0";
    step.crc16Enabled = crc16CheckBox->isChecked();
    step.compareEnabled = compareCheckBox->isChecked();

    // 如果设备名称发生变化，同步更新设备列表和映射关系
    if (oldDeviceName != step.device && !step.device.isEmpty()) {
      updateDeviceNameInLists(oldDeviceName, step.device);

      // 更新设备下拉框 - 使用更安全的方式，避免触发信号
      QString currentSerialPort = serialPortComboBox->currentText();
      QString currentDevice = deviceComboBox->currentText();

      // 临时断开信号连接，避免触发串口变化事件
      disconnect(serialPortComboBox,
                 QOverload<int>::of(&QComboBox::currentIndexChanged), this,
                 &MappingWindow::onSerialPortChanged);

      if (currentSerialPort == "COMA") {
        deviceComboBox->clear();
        deviceComboBox->addItems(controllerDevices);
      } else if (currentSerialPort == "COMB") {
        deviceComboBox->clear();
        deviceComboBox->addItems(protectionDevices);
      }

      // 恢复信号连接
      connect(serialPortComboBox,
              QOverload<int>::of(&QComboBox::currentIndexChanged), this,
              &MappingWindow::onSerialPortChanged);

      // 保存设备列表到文件
      saveDeviceListsToFile();
    }

    updateTestStepsList();
    updateGraphicalMapping(-1);
  }
}

// 事件过滤器，处理流程图的单击和双击事件
bool MappingWindow::eventFilter(QObject *obj, QEvent *event) {
  if (obj == view) {
    if (event->type() == QEvent::MouseButtonPress) {
      QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
      if (mouseEvent->button() == Qt::LeftButton) {
        // 处理左键单击，用于选择节点
        QPointF scenePos = view->mapToScene(mouseEvent->pos());
        QGraphicsItem *item = scene->itemAt(scenePos, QTransform());

        if (item && item->data(0).isValid()) {
          // 点击在节点上，选择该节点
          int stepIndex = item->data(0).toInt();
          if (stepIndex >= 0 && stepIndex < testSteps.size()) {
            selectedNodeIndex = stepIndex;
            updateGraphicalMapping(-1); // 重新绘制以显示选中状态
            logMessage(QString("已选中节点: %1 - %2")
                           .arg(stepIndex + 1)
                           .arg(testSteps[stepIndex].device),
                       "INFO");
          }
        } else {
          // 点击在空白区域，取消选择
          if (selectedNodeIndex != -1) {
            selectedNodeIndex = -1;
            updateGraphicalMapping(-1); // 重新绘制以清除选中状态
            logMessage("已取消节点选择", "INFO");
          }
        }
        return false; // 继续传递事件，以便处理双击
      }
    } else if (event->type() == QEvent::MouseButtonDblClick) {
      QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
      if (mouseEvent->button() == Qt::LeftButton) {
        // 将鼠标位置转换为场景坐标
        QPointF scenePos = view->mapToScene(mouseEvent->pos());
        onGraphicsViewDoubleClicked(scenePos);
        return true; // 事件已处理
      }
    }
  }

  // 调用基类的事件过滤器
  return QMainWindow::eventFilter(obj, event);
}

void MappingWindow::onFlowchartContextMenuRequested(const QPoint &pos) {
  // 将视图坐标转换为场景坐标
  QPointF scenePos = view->mapToScene(pos);

  // 查找点击位置的图形项
  QGraphicsItem *item = scene->itemAt(scenePos, QTransform());

  // 创建右键菜单
  QMenu contextMenu(this);

  // 添加映射操作
  QAction *addMappingAction = contextMenu.addAction("添加映射");
  addMappingAction->setIcon(QIcon(":/icons/add.svg"));

  QAction *deleteMappingAction = nullptr;

  // 如果点击在节点上，添加删除映射选项
  if (item && item->data(0).isValid()) {
    int stepIndex = item->data(0).toInt();
    if (stepIndex >= 0 && stepIndex < testSteps.size()) {
      deleteMappingAction = contextMenu.addAction(
          QString("删除映射 - %1").arg(testSteps[stepIndex].device));
      deleteMappingAction->setIcon(QIcon(":/icons/delete.svg"));
    }
  }

  // 添加分隔符
  contextMenu.addSeparator();

  // 添加其他操作
  QAction *refreshAction = contextMenu.addAction("刷新流程图");
  refreshAction->setIcon(QIcon(":/icons/refresh.svg"));

  // 显示菜单并处理选择
  QAction *selectedAction = contextMenu.exec(view->mapToGlobal(pos));

  if (selectedAction == addMappingAction) {
    onFlowchartAddMapping();
  } else if (selectedAction == deleteMappingAction && item) {
    // 设置要删除的步骤索引
    int stepIndex = item->data(0).toInt();
    if (stepIndex >= 0 && stepIndex < testSteps.size()) {
      // 找到对应的映射关系并删除
      QString deviceToDelete = testSteps[stepIndex].device;
      for (int i = 0; i < mappingRelations.size(); ++i) {
        if (mappingRelations[i].device == deviceToDelete) {
          // 保存当前状态到撤销栈
          undoMappingStack.push(mappingRelations);
          redoMappingStack.clear();

          // 删除映射关系
          mappingRelations.removeAt(i);

          // 更新界面
          updateMappingList();
          generateTestSteps();
          updateGraphicalMapping(-1);
          updateTestStepsList();

          logMessage(QString("已删除映射: %1").arg(deviceToDelete), "INFO");
          break;
        }
      }
    }
  } else if (selectedAction == refreshAction) {
    // 刷新流程图
    updateGraphicalMapping(-1);
    logMessage("流程图已刷新", "INFO");
  }
}

void MappingWindow::onFlowchartAddMapping() {
  // 根据选中的节点确定插入位置
  int insertPosition = mappingRelations.size(); // 默认插入到最后

  if (selectedNodeIndex >= 0 && selectedNodeIndex < testSteps.size()) {
    // 如果有选中的节点，插入到该节点对应的映射之后
    QString selectedDevice = testSteps[selectedNodeIndex].device;

    // 找到对应的映射关系位置
    for (int i = 0; i < mappingRelations.size(); ++i) {
      if (mappingRelations[i].device == selectedDevice) {
        insertPosition = i + 1; // 插入到该映射之后
        break;
      }
    }

    logMessage(QString("将在节点 %1 (%2) 之后插入新映射")
                   .arg(selectedNodeIndex + 1)
                   .arg(selectedDevice),
               "INFO");
  } else {
    logMessage("将在流程图末尾插入新映射", "INFO");
  }

  // 调用插入新映射功能，传入计算出的插入位置
  onInsertMappingAtPosition(insertPosition);
}

void MappingWindow::onFlowchartDeleteMapping() {
  // 这个函数暂时不需要实现，因为删除操作在右键菜单中直接处理
}

void MappingWindow::onInsertMappingAtPosition(int position) {
  // 弹窗选择串口和设备
  QDialog dialog(this);
  dialog.setWindowTitle("插入新映射");
  QVBoxLayout *layout = new QVBoxLayout(&dialog);
  QComboBox *portCombo = new QComboBox(&dialog);
  portCombo->addItem("COMA");
  portCombo->addItem("COMB");
  QComboBox *deviceCombo = new QComboBox(&dialog);
  // 默认显示控制器设备
  deviceCombo->addItems(controllerDevices);
  connect(portCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
          [&](int idx) {
            deviceCombo->clear();
            if (idx == 0)
              deviceCombo->addItems(controllerDevices);
            else
              deviceCombo->addItems(protectionDevices);
          });
  layout->addWidget(new QLabel("串口："));
  layout->addWidget(portCombo);
  layout->addWidget(new QLabel("设备："));
  layout->addWidget(deviceCombo);
  QDialogButtonBox *btnBox = new QDialogButtonBox(
      QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &dialog);
  layout->addWidget(btnBox);
  connect(btnBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
  connect(btnBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

  if (dialog.exec() == QDialog::Accepted) {
    QString serialPort = portCombo->currentText();
    QString device = deviceCombo->currentText();
    QString deviceType =
        (portCombo->currentIndex() == 0) ? "controller" : "protection";

    // 检查是否已存在
    for (const auto &rel : mappingRelations) {
      if (rel.serialPort == serialPort && rel.device == device) {
        QMessageBox::warning(this, "重复映射", "该映射已存在！");
        return;
      }
    }

    // 保存当前状态到撤销栈
    QList<MappingRelation> oldMappingRelations = mappingRelations;

    // 确保插入位置有效
    if (position < 0)
      position = 0;
    if (position > mappingRelations.size())
      position = mappingRelations.size();

    // 在指定位置插入新映射
    mappingRelations.insert(position,
                            MappingRelation(serialPort, device, deviceType));

    undoMappingStack.push(oldMappingRelations);
    redoMappingStack.clear();
    updateMappingList();
    updateGraphicalMapping(-1);
    generateTestSteps();

    logMessage(QString("已在位置 %1 插入新映射: %2 -> %3")
                   .arg(position + 1)
                   .arg(serialPort)
                   .arg(device),
               "INFO");
  }
}

void MappingWindow::showTestReport() {
  QString reportContent = generateTestReport();

  // 创建测试报告对话框
  QDialog reportDialog(this);
  reportDialog.setWindowTitle("JcSoft 自动化测试报告");
  reportDialog.setModal(true);
  reportDialog.resize(1400, 900);                   // 进一步增大对话框尺寸
  reportDialog.setWindowState(Qt::WindowMaximized); // 默认最大化窗口

  QVBoxLayout *layout = new QVBoxLayout(&reportDialog);

  // 创建文本显示区域
  QTextEdit *reportTextEdit = new QTextEdit;
  reportTextEdit->setReadOnly(true);
  reportTextEdit->setHtml(reportContent);
  reportTextEdit->setLineWrapMode(
      QTextEdit::NoWrap); // 禁用自动换行以保持表格格式
  layout->addWidget(reportTextEdit);

  // 添加按钮
  QHBoxLayout *buttonLayout = new QHBoxLayout;
  QPushButton *exportButton = new QPushButton("📄 导出报告");
  QPushButton *printButton = new QPushButton("🖨️ 打印报告");
  QPushButton *minimizeButton = new QPushButton("🗕 最小化");
  QPushButton *maximizeButton = new QPushButton("🗗 最大化");
  QPushButton *closeButton = new QPushButton("❌ 关闭");

  // 设置按钮样式
  exportButton->setStyleSheet(
      "QPushButton { padding: 8px 16px; border-radius: 4px; background-color: "
      "#3498db; color: white; } QPushButton:hover { background-color: #2980b9; "
      "}");
  printButton->setStyleSheet(
      "QPushButton { padding: 8px 16px; border-radius: 4px; background-color: "
      "#27ae60; color: white; } QPushButton:hover { background-color: #229954; "
      "}");
  minimizeButton->setStyleSheet(
      "QPushButton { padding: 8px 16px; border-radius: 4px; background-color: "
      "#f39c12; color: white; } QPushButton:hover { background-color: #e67e22; "
      "}");
  maximizeButton->setStyleSheet(
      "QPushButton { padding: 8px 16px; border-radius: 4px; background-color: "
      "#9b59b6; color: white; } QPushButton:hover { background-color: #8e44ad; "
      "}");
  closeButton->setStyleSheet(
      "QPushButton { padding: 8px 16px; border-radius: 4px; background-color: "
      "#e74c3c; color: white; } QPushButton:hover { background-color: #c0392b; "
      "}");

  buttonLayout->addStretch();
  buttonLayout->addWidget(exportButton);
  buttonLayout->addWidget(printButton);
  buttonLayout->addWidget(minimizeButton);
  buttonLayout->addWidget(maximizeButton);
  buttonLayout->addWidget(closeButton);
  layout->addLayout(buttonLayout);

  // 连接按钮事件
  connect(closeButton, &QPushButton::clicked, &reportDialog, &QDialog::accept);
  connect(minimizeButton, &QPushButton::clicked, &reportDialog,
          &QDialog::showMinimized);
  connect(maximizeButton, &QPushButton::clicked, [&reportDialog]() {
    if (reportDialog.windowState() & Qt::WindowMaximized) {
      reportDialog.showNormal();
    } else {
      reportDialog.showMaximized();
    }
  });

  // 导出报告功能
  connect(exportButton, &QPushButton::clicked, [&]() {
    QString fileName = QFileDialog::getSaveFileName(
        this, "导出测试报告",
        QCoreApplication::applicationDirPath() + "/dev_doc/" +
            QString("JcSoft测试报告_%1.html")
                .arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        "HTML文件 (*.html);;所有文件 (*.*)");
    if (!fileName.isEmpty()) {
      QFile file(fileName);
      if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out.setEncoding(QStringConverter::Utf8);
        out << reportContent;
        file.close();
        QMessageBox::information(this, "导出成功",
                                 "测试报告已成功导出到：\n" + fileName);
      } else {
        QMessageBox::warning(this, "导出失败",
                             "无法写入文件：\n" + fileName + "\n错误信息：" +
                                 file.errorString());
      }
    }
  });

  // 打印报告功能
  connect(printButton, &QPushButton::clicked, [&]() {
    QPrinter printer(QPrinter::HighResolution);
    printer.setPageOrientation(QPageLayout::Portrait);
    printer.setPageSize(QPageSize::A4);

    QPrintDialog printDialog(&printer, this);
    if (printDialog.exec() == QDialog::Accepted) {
      reportTextEdit->print(&printer);
    }
  });

  reportDialog.exec();
}

QString MappingWindow::generateTestReport() {
  QString html;
  html += "<!DOCTYPE html><html><head><meta charset='UTF-8'>";
  html += "<title>JcSoft 测试报告</title>";
  html += "<style>";
  html += "body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: "
          "20px; background-color: #f5f5f5; }";
  html += ".container { max-width: 1200px; margin: 0 auto; background-color: "
          "white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px "
          "rgba(0,0,0,0.1); }";
  html += "h1 { color: #2c3e50; text-align: center; border-bottom: 3px solid "
          "#3498db; padding-bottom: 15px; margin-bottom: 30px; }";
  html += "h2 { color: #34495e; margin-top: 30px; border-left: 4px solid "
          "#3498db; padding-left: 15px; }";
  html += "h3 { color: #2c3e50; margin-top: 25px; }";
  html += ".summary { background: linear-gradient(135deg, #667eea 0%, #764ba2 "
          "100%); color: white; padding: 20px; border-radius: 8px; margin: "
          "20px 0; }";
  html += ".summary h2 { color: white; border-left: none; }";
  html += ".passed { color: #27ae60; font-weight: bold; }";
  html += ".failed { color: #e74c3c; font-weight: bold; }";
  html += ".pending { color: #f39c12; font-weight: bold; }";
  html += ".info { color: #3498db; font-weight: bold; }";
  html += "table { width: 100%; border-collapse: collapse; margin: 20px 0; "
          "border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px "
          "rgba(0,0,0,0.1); }";
  html += "th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }";
  html += "th { background: linear-gradient(135deg, #3498db, #2980b9); color: "
          "black; font-weight: bold; }";
  html += "tr:nth-child(even) { background-color: #f8f9fa; }";
  html += "tr:hover { background-color: #e3f2fd; }";
  html += ".device-name { font-weight: bold; color: #2c3e50; }";
  html += ".step-number { background-color: #3498db; color: white; padding: "
          "4px 8px; border-radius: 4px; font-size: 12px; }";
  html += ".status-badge { padding: 4px 8px; border-radius: 4px; font-size: "
          "12px; font-weight: bold; }";
  html += ".status-passed { background-color: #d4edda; color: #155724; }";
  html += ".status-failed { background-color: #f8d7da; color: #721c24; }";
  html += ".status-pending { background-color: #fff3cd; color: #856404; }";
  html += ".status-info { background-color: #d1ecf1; color: #0c5460; }";
  html += ".compare-enabled { background-color: #d4edda; color: #155724; }";
  html += ".compare-disabled { background-color: #f8d7da; color: #721c24; }";
  html += ".crc-enabled { background-color: #d4edda; color: #155724; }";
  html += ".crc-disabled { background-color: #f8d7da; color: #721c24; }";
  html += ".data-cell { font-family: 'Courier New', monospace; font-size: "
          "11px; background-color: #f8f9fa; }";
  html += ".timestamp { color: #7f8c8d; font-size: 14px; }";
  html += "</style></head><body>";

  html += "<div class='container'>";

  // 报告标题
  html += "<h1>🔧 JcSoft 自动化测试报告</h1>";

  // 生成时间
  html += QString("<p class='timestamp'><strong>📅 生成时间：</strong>%1</p>")
              .arg(QDateTime::currentDateTime().toString(
                  "yyyy年MM月dd日 hh:mm:ss"));

  // 统计信息
  int totalTests = testSteps.size();
  int passedTests = 0;
  int failedTests = 0;
  int pendingTests = 0;
  int infoTests = 0;
  int compareEnabledTests = 0;
  int crcEnabledTests = 0;

  for (const TestStep &step : testSteps) {
    if (step.type == "info") {
      infoTests++;
    } else if (step.passed) {
      passedTests++;
    } else if (!step.actual.isEmpty()) {
      failedTests++;
    } else {
      pendingTests++;
    }

    if (step.compareEnabled)
      compareEnabledTests++;
    if (step.crc16Enabled)
      crcEnabledTests++;
  }

  int actualTestSteps = totalTests - infoTests; // 排除信息步骤

  html += "<div class='summary'>";
  html += "<h2>📊 测试概要</h2>";
  html += QString("<p><strong>总步骤数：</strong>%1 "
                  "(其中信息步骤：%2，实际测试步骤：%3)</p>")
              .arg(totalTests)
              .arg(infoTests)
              .arg(actualTestSteps);
  html +=
      QString("<p><span class='passed'>✅ 通过：%1</span> | ").arg(passedTests);
  html +=
      QString("<span class='failed'>❌ 失败：%1</span> | ").arg(failedTests);
  html += QString("<span class='pending'>⏳ 未执行：%1</span></p>")
              .arg(pendingTests);

  if (actualTestSteps > 0) {
    double passRate = (double)passedTests / actualTestSteps * 100;
    html += QString("<p><strong>通过率：</strong>%.1f%%</p>").arg(passRate);
  }

  html += QString("<p><strong>数据比对启用：</strong>%1 个步骤</p>")
              .arg(compareEnabledTests);
  html += QString("<p><strong>CRC16校验启用：</strong>%1 个步骤</p>")
              .arg(crcEnabledTests);
  html += "</div>";

  // 详细测试步骤表格
  html += "<h2>📋 详细测试步骤</h2>";
  html += "<table>";
  html += "<tr><th>步骤</th><th>设备名称</th><th>测试类型</th><th>数据比对</"
          "th><th>发送数据</th><th>期望数据</th><th>实际数据</th><th>测试结果</"
          "th><th>备注</th></tr>";

  for (int i = 0; i < testSteps.size(); ++i) {
    const TestStep &step = testSteps[i];

    // 步骤编号
    QString stepNumber =
        QString("<span class='step-number'>%1</span>").arg(i + 1);

    // 设备名称
    QString deviceName = step.device.isEmpty() ? "信息步骤" : step.device;

    // 测试类型
    QString testType;
    if (step.type == "info") {
      testType = "<span class='status-info'>信息</span>";
    } else if (step.type.contains("controller")) {
      testType = "<span class='status-badge' style='background-color: #e8f5e8; "
                 "color: #2e7d32;'>控制器</span>";
    } else if (step.type.contains("protection")) {
      testType = "<span class='status-badge' style='background-color: #fff3e0; "
                 "color: #ef6c00;'>保护装置</span>";
    } else {
      testType = "<span class='status-badge' style='background-color: #f3e5f5; "
                 "color: #7b1fa2;'>其他</span>";
    }

    // 数据比对状态
    QString compareStatus;
    if (step.type == "info") {
      compareStatus = "-";
    } else if (step.compareEnabled) {
      compareStatus = "<span class='compare-enabled'>启用</span>";
    } else {
      compareStatus = "<span class='compare-disabled'>禁用</span>";
    }

    // 发送数据
    QString sendData =
        step.sendData.isEmpty()
            ? "-"
            : QString("<span class='data-cell'>%1</span>").arg(step.sendData);

    // 期望数据
    QString expectData = step.expectReceiveData.isEmpty()
                             ? "-"
                             : QString("<span class='data-cell'>%1</span>")
                                   .arg(step.expectReceiveData);

    // 实际数据 - 优先显示串口A或串口B的数据，如果都为空则显示通用字段
    QString actualData;
    if (!step.actualReceiveA.isEmpty()) {
      actualData = QString("<span class='data-cell'>%1 (串口A)</span>")
                       .arg(step.actualReceiveA);
    } else if (!step.actualReceiveB.isEmpty()) {
      actualData = QString("<span class='data-cell'>%1 (串口B)</span>")
                       .arg(step.actualReceiveB);
    } else if (!step.actualReceiveData.isEmpty()) {
      actualData = QString("<span class='data-cell'>%1</span>")
                       .arg(step.actualReceiveData);
    } else {
      actualData = "-";
    }

    // 测试结果
    QString testResult;
    if (step.type == "info") {
      testResult = "<span class='status-info'>信息步骤</span>";
    } else if (step.passed) {
      testResult = "<span class='status-passed'>✅ 通过</span>";
    } else if (!step.actual.isEmpty()) {
      testResult = "<span class='status-failed'>❌ 失败</span>";
    } else {
      testResult = "<span class='status-pending'>⏳ 未执行</span>";
    }

    // 备注信息
    QString notes = step.notes.isEmpty() ? "-" : step.notes;

    html += "<tr>";
    html += QString("<td>%1</td>").arg(stepNumber);
    html += QString("<td class='device-name'>%1</td>").arg(deviceName);
    html += QString("<td>%1</td>").arg(testType);
    html += QString("<td>%1</td>").arg(compareStatus);
    html += QString("<td>%1</td>").arg(sendData);
    html += QString("<td>%1</td>").arg(expectData);
    html += QString("<td>%1</td>").arg(actualData);
    html += QString("<td>%1</td>").arg(testResult);
    html += QString("<td>%1</td>").arg(notes);
    html += "</tr>";
  }

  html += "</table>";

  // 按设备分组统计
  html += "<h2>📈 设备测试统计</h2>";
  QMap<QString, QList<TestStep>> deviceGroups;
  QList<QString> deviceOrder; // 新增：记录设备出现顺序
  QSet<QString> deviceSet;    // 新增：辅助去重

  // 按设备名称分组（排除信息步骤），并记录顺序
  for (const TestStep &step : testSteps) {
    if (step.type != "info" && !step.device.isEmpty()) {
      QString deviceName = step.device;
      // 保留完整设备名称（包括括号中的说明）
      if (!deviceSet.contains(deviceName)) {
        deviceOrder.append(deviceName);
        deviceSet.insert(deviceName);
      }
      deviceGroups[deviceName].append(step);
    }
  }

  if (!deviceGroups.isEmpty()) {
    html += "<table>";
    html += "<tr><th>设备名称</th><th>测试步骤数</th><th>通过</th><th>失败</"
            "th><th>未执行</th><th>通过率</th><th>状态</th></tr>";

    for (const QString &deviceName : deviceOrder) {
      QList<TestStep> deviceSteps = deviceGroups[deviceName];

      int deviceTotal = deviceSteps.size();
      int devicePassed = 0;
      int deviceFailed = 0;
      int devicePending = 0;

      for (const TestStep &step : deviceSteps) {
        if (step.passed) {
          devicePassed++;
        } else if (!step.actual.isEmpty()) {
          deviceFailed++;
        } else {
          devicePending++;
        }
      }

      double devicePassRate =
          deviceTotal > 0 ? (double)devicePassed / deviceTotal * 100 : 0;

      QString deviceStatus;
      if (deviceFailed > 0) {
        deviceStatus = "<span class='status-failed'>❌ 未通过</span>";
      } else if (devicePending > 0) {
        deviceStatus = "<span class='status-pending'>⏳ 未完成</span>";
      } else if (devicePassed > 0) {
        deviceStatus = "<span class='status-passed'>✅ 通过</span>";
      } else {
        deviceStatus = "<span class='status-pending'>⏳ 无测试</span>";
      }

      html += "<tr>";
      html += QString("<td class='device-name'>%1</td>").arg(deviceName);
      html += QString("<td>%1</td>").arg(deviceTotal);
      html += QString("<td class='passed'>%1</td>").arg(devicePassed);
      html += QString("<td class='failed'>%1</td>").arg(deviceFailed);
      html += QString("<td class='pending'>%1</td>").arg(devicePending);
      html += QString("<td>%.1f%%</td>").arg(devicePassRate);
      html += QString("<td>%1</td>").arg(deviceStatus);
      html += "</tr>";
    }
    html += "</table>";
  } else {
    html += "<p class='info'>暂无设备测试数据</p>";
  }

  // 测试配置信息
  html += "<h2>⚙️ 测试配置信息</h2>";
  html += "<table>";
  html += "<tr><th>配置项</th><th>值</th></tr>";
  html += QString("<tr><td>全局CRC16校验</td><td>%1</td></tr>")
              .arg(ui->globalCrc16CheckBox->isChecked() ? "启用" : "禁用");
  html += QString("<tr><td>测试延时</td><td>%1 ms</td></tr>")
              .arg(ui->testDelaySpinBox->value());
  html += QString("<tr><td>映射关系数量</td><td>%1</td></tr>")
              .arg(mappingRelations.size());
  html += "</table>";

  html += "</div></body></html>";

  return html;
}

void MappingWindow::onFlowchartOnlyCheckBoxChanged(int state) {
  bool onlyFlowchart = (state == Qt::Checked);
  ui->mappingWidget->setVisible(!onlyFlowchart);
  ui->logGroupBox->setVisible(!onlyFlowchart);
  // mainSplitter: mappingWidget, flowchartGroupBox, logGroupBox
  if (onlyFlowchart) {
    QList<int> sizes;
    sizes << 0 << 1 << 0; // 只显示流程图
    ui->mainSplitter->setSizes(sizes);
  } else {
    QList<int> sizes;
    sizes << 1 << 1 << 1;
    ui->mainSplitter->setSizes(sizes);
  }
}