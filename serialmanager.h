#ifndef SERIALMANAGER_H
#define SERIALMANAGER_H

#include <QObject>
#include <QMap>
#include <QSerialPort>
#include <QString>
#include <QStringList>
#include <QByteArray>
#include "modbusserial.h"
#include "modbusserialbase.h"

class SerialManager : public QObject
{
    Q_OBJECT

public:
    explicit SerialManager(QObject *parent = nullptr);
    ~SerialManager();

    bool connectPort(const QString &portId, const QString &portName, int baudRate,
                     const QString &parityText, int dataBitsValue, const QString &stopBitsText);
    void disconnectPort(const QString &portId);
    bool sendData(const QString &portId, const QByteArray &data);
    bool isConnected(const QString &portId) const;
    QStringList getAvailablePorts();

    // 获取具体的串口对象，供其他模块使用
    ModbusSerialA* getSerialA() const;
    ModbusSerialB* getSerialB() const;

signals:
    void connectionStateChanged(const QString &portId, bool connected);
    void errorOccurred(const QString &portId, const QString &error);
    void dataReceived(const QString &portId, const QString &message);
    void dataSent(const QString &portId, const QString &message);

private:
    void setupSerialPort(const QString &portId, ModbusSerialBase *serial);

    QMap<QString, ModbusSerialBase*> m_serialPorts;
};

#endif // SERIALMANAGER_H
