{"mapping": [{"device": "J1 (DCS OFF 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J1 (DCS OFF 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J2 (SIS OFF 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J2 (SIS OFF 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J3 (OTHER OFF 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J3 (OTHER OFF 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J4 (手、自动 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J4 (手、自动 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J5 (SB1 手合 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J5 (SB1 手合 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J6 (SB2 手分 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J6 (SB2 手分 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J7 (DCS ON 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J7 (DCS ON 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J8 (ACS 自启动合 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J8 (ACS 自启动合 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J9 (DCS 准备好 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J9 (DCS 准备好 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J10 (备用 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J10 (备用 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J11 (备用 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J11 (备用 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J12 (备用 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J12 (备用 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J13 (备用 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J13 (备用 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J14 (备用 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J14 (备用 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J15 (备用 遥控合)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J15 (备用 遥控分)", "deviceType": "controller", "serialPort": "计算机(COMA)"}, {"device": "J16 (CLEAR)", "deviceType": "controller", "serialPort": "计算机(COMA)"}], "testSteps": [{"actual": "", "actualReceiveData": "", "command": "测试开始", "compareEnabled": false, "crc16Enabled": true, "device": "", "expectReceiveData": "", "expected": "", "functionCode": 0, "notes": "自动生成的测试流程", "passed": false, "sendData": "", "stationId": 221, "targetSerialPort": "A", "type": "info"}, {"actual": "", "actualReceiveData": "", "command": "DD-J1 (DCS OFF 遥控合) 遥控合", "compareEnabled": true, "crc16Enabled": true, "device": "J1 (DCS OFF 遥控合)", "expectReceiveData": "DD 05 00 00 FF 01 9F 66", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J1 (DCS OFF 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 00 FF 00 9F 66", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J1 (DCS OFF 遥控分) 遥控分", "compareEnabled": true, "crc16Enabled": true, "device": "J1 (DCS OFF 遥控分)", "expectReceiveData": "DD 05 00 00 00 00 DE 96", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J1 (DCS OFF 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 00 00 00 DE 96", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J2 (SIS OFF 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J2 (SIS OFF 遥控合)", "expectReceiveData": "DD 05 00 01 FF 00 CE A6", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J2 (SIS OFF 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 01 FF 00 CE A6", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J2 (SIS OFF 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J2 (SIS OFF 遥控分)", "expectReceiveData": "DD 05 00 01 00 00 8F 56", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J2 (SIS OFF 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 01 00 00 8F 56", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J3 (OTHER OFF 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J3 (OTHER OFF 遥控合)", "expectReceiveData": "DD 05 00 02 FF 00 3E A6", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J3 (OTHER OFF 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 02 FF 00 3E A6", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J3 (OTHER OFF 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J3 (OTHER OFF 遥控分)", "expectReceiveData": "DD 05 00 02 00 00 7F 56", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J3 (OTHER OFF 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 02 00 00 7F 56", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J4 (手、自动 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J4 (手、自动 遥控合)", "expectReceiveData": "DD 05 00 03 FF 00 6F 66", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J4 (手、自动 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 03 FF 00 6F 66", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J4 (手、自动 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J4 (手、自动 遥控分)", "expectReceiveData": "DD 05 00 03 00 00 2E 96", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J4 (手、自动 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 03 00 00 2E 96", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J5 (SB1 手合 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J5 (SB1 手合 遥控合)", "expectReceiveData": "DD 05 00 04 FF 00 DE A7", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J5 (SB1 手合 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 04 FF 00 DE A7", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J5 (SB1 手合 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J5 (SB1 手合 遥控分)", "expectReceiveData": "DD 05 00 04 00 00 9F 57", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J5 (SB1 手合 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 04 00 00 9F 57", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J6 (SB2 手分 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J6 (SB2 手分 遥控合)", "expectReceiveData": "DD 05 00 05 FF 00 8F 67", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J6 (SB2 手分 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 05 FF 00 8F 67", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J6 (SB2 手分 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J6 (SB2 手分 遥控分)", "expectReceiveData": "DD 05 00 05 00 00 CE 97", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J6 (SB2 手分 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 05 00 00 CE 97", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J7 (DCS ON 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J7 (DCS ON 遥控合)", "expectReceiveData": "DD 05 00 06 FF 00 7F 67", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J7 (DCS ON 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 06 FF 00 7F 67", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J7 (DCS ON 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J7 (DCS ON 遥控分)", "expectReceiveData": "DD 05 00 06 00 00 3E 97", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J7 (DCS ON 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 06 00 00 3E 97", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J8 (ACS 自启动合 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J8 (ACS 自启动合 遥控合)", "expectReceiveData": "DD 05 00 07 FF 00 2E A7", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J8 (ACS 自启动合 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 07 FF 00 2E A7", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J8 (ACS 自启动合 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J8 (ACS 自启动合 遥控分)", "expectReceiveData": "DD 05 00 07 00 00 6F 57", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J8 (ACS 自启动合 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 07 00 00 6F 57", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J9 (DCS 准备好 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J9 (DCS 准备好 遥控合)", "expectReceiveData": "DD 05 00 08 FF 00 1E A4", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J9 (DCS 准备好 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 08 FF 00 1E A4", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J9 (DCS 准备好 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J9 (DCS 准备好 遥控分)", "expectReceiveData": "DD 05 00 08 00 00 5F 54", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J9 (DCS 准备好 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 08 00 00 5F 54", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J10 (备用 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J10 (备用 遥控合)", "expectReceiveData": "DD 05 00 09 FF 00 4F 64", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J10 (备用 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 09 FF 00 4F 64", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J10 (备用 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J10 (备用 遥控分)", "expectReceiveData": "DD 05 00 09 00 00 0E 94", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J10 (备用 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 09 00 00 0E 94", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J11 (备用 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J11 (备用 遥控合)", "expectReceiveData": "DD 05 00 0A FF 00 BF 64", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J11 (备用 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 0A FF 00 BF 64", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J11 (备用 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J11 (备用 遥控分)", "expectReceiveData": "DD 05 00 0A 00 00 FE 94", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J11 (备用 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 0A 00 00 FE 94", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J12 (备用 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J12 (备用 遥控合)", "expectReceiveData": "DD 05 00 0B FF 00 EE A4", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J12 (备用 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 0B FF 00 EE A4", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J12 (备用 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J12 (备用 遥控分)", "expectReceiveData": "DD 05 00 0B 00 00 AF 54", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J12 (备用 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 0B 00 00 AF 54", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J13 (备用 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J13 (备用 遥控合)", "expectReceiveData": "DD 05 00 0C FF 00 5F 65", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J13 (备用 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 0C FF 00 5F 65", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J13 (备用 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J13 (备用 遥控分)", "expectReceiveData": "DD 05 00 0C 00 00 1E 95", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J13 (备用 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 0C 00 00 1E 95", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J14 (备用 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J14 (备用 遥控合)", "expectReceiveData": "DD 05 00 0D FF 00 0E A5", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J14 (备用 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 0D FF 00 0E A5", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J14 (备用 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J14 (备用 遥控分)", "expectReceiveData": "DD 05 00 0D 00 00 4F 55", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J14 (备用 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 0D 00 00 4F 55", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J15 (备用 遥控合) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J15 (备用 遥控合)", "expectReceiveData": "DD 05 00 0E FF 00 FE A5", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J15 (备用 遥控合)遥控合操作", "passed": false, "sendData": "DD 05 00 0E FF 00 FE A5", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J15 (备用 遥控分) 遥控分", "compareEnabled": false, "crc16Enabled": true, "device": "J15 (备用 遥控分)", "expectReceiveData": "DD 05 00 0E 00 00 BF 55", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J15 (备用 遥控分)遥控分操作", "passed": false, "sendData": "DD 05 00 0E 00 00 BF 55", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "DD-J16 (CLEAR) 遥控合", "compareEnabled": false, "crc16Enabled": true, "device": "J16 (CLEAR)", "expectReceiveData": "DD 06 00 00 80 00 FB 56", "expected": "成功", "functionCode": 5, "notes": "计算机(COMA)→控制器J16 (CLEAR)遥控合操作", "passed": false, "sendData": "DD 06 00 00 80 00 FB 56", "stationId": 221, "targetSerialPort": "A", "type": "controller_coma"}, {"actual": "", "actualReceiveData": "", "command": "测试结束", "compareEnabled": false, "crc16Enabled": true, "device": "", "expectReceiveData": "", "expected": "", "functionCode": 0, "notes": "所有测试项目已完成", "passed": false, "sendData": "", "stationId": 221, "targetSerialPort": "A", "type": "info"}]}