#include "datamappingrulesdialog.h"
#include <QDebug>
#include <QApplication>

DataMappingRulesDialog::DataMappingRulesDialog(const QString &deviceModel, const QStringList &headers, const QStringList &rowNames, QWidget *parent)
    : QDialog(parent)
    , currentDeviceModel(deviceModel)
    , configModified(false)
    , excelHeaders(headers)
    , csvRowNames(rowNames)
{
    setWindowTitle("数据对应规则配置");
    setModal(true);
    resize(800, 600);
    
    setupUI();
    loadConfigs();
    updateConfigComboBox();
    updateButtonStates();
}

DataMappingRulesDialog::~DataMappingRulesDialog()
{
}

void DataMappingRulesDialog::setupUI()
{
    mainLayout = new QVBoxLayout(this);
    
    // 配置管理区域
    configGroup = new QGroupBox("配置管理");
    configLayout = new QHBoxLayout(configGroup);
    
    configLabel = new QLabel("选择配置:");
    configComboBox = new QComboBox();
    configComboBox->setMinimumWidth(200);
    connect(configComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &DataMappingRulesDialog::onConfigSelectionChanged);
    
    newConfigButton = new QPushButton("新建配置");
    connect(newConfigButton, &QPushButton::clicked, this, &DataMappingRulesDialog::onNewConfigClicked);
    
    saveConfigButton = new QPushButton("保存配置");
    connect(saveConfigButton, &QPushButton::clicked, this, &DataMappingRulesDialog::onSaveConfigClicked);
    
    saveAsConfigButton = new QPushButton("另存为");
    connect(saveAsConfigButton, &QPushButton::clicked, this, &DataMappingRulesDialog::onSaveAsConfigClicked);
    
    deleteConfigButton = new QPushButton("删除配置");
    deleteConfigButton->setStyleSheet("QPushButton { background-color: #e74c3c; color: white; }");
    connect(deleteConfigButton, &QPushButton::clicked, this, &DataMappingRulesDialog::onDeleteConfigClicked);
    
    configLayout->addWidget(configLabel);
    configLayout->addWidget(configComboBox);
    configLayout->addStretch();
    configLayout->addWidget(newConfigButton);
    configLayout->addWidget(saveConfigButton);
    configLayout->addWidget(saveAsConfigButton);
    configLayout->addWidget(deleteConfigButton);
    
    mainLayout->addWidget(configGroup);
    
    // 映射配置区域
    mappingGroup = new QGroupBox("Excel列与CSV行映射关系");
    mappingLayout = new QVBoxLayout(mappingGroup);
    
    // 映射表格
    mappingTable = new QTableWidget();
    mappingTable->setColumnCount(3);
    QStringList headers;
    headers << "Excel列序号" << "CSV行号" << "描述";
    mappingTable->setHorizontalHeaderLabels(headers);
    mappingTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    mappingTable->setAlternatingRowColors(true);
    mappingTable->horizontalHeader()->setStretchLastSection(true);
    mappingTable->setColumnWidth(0, 120);
    mappingTable->setColumnWidth(1, 120);
    
    connect(mappingTable, &QTableWidget::itemSelectionChanged,
            this, &DataMappingRulesDialog::onTableSelectionChanged);
    connect(mappingTable, &QTableWidget::itemDoubleClicked,
            this, &DataMappingRulesDialog::onEditMappingClicked);
    
    mappingLayout->addWidget(mappingTable);
    
    // 映射操作按钮
    mappingButtonLayout = new QHBoxLayout();
    
    addMappingButton = new QPushButton("添加映射");
    addMappingButton->setStyleSheet("QPushButton { background-color: #27ae60; color: white; }");
    connect(addMappingButton, &QPushButton::clicked, this, &DataMappingRulesDialog::onAddMappingClicked);
    
    editMappingButton = new QPushButton("编辑映射");
    connect(editMappingButton, &QPushButton::clicked, this, &DataMappingRulesDialog::onEditMappingClicked);
    
    deleteMappingButton = new QPushButton("删除映射");
    deleteMappingButton->setStyleSheet("QPushButton { background-color: #e74c3c; color: white; }");
    connect(deleteMappingButton, &QPushButton::clicked, this, &DataMappingRulesDialog::onDeleteMappingClicked);
    
    resetButton = new QPushButton("重置");
    connect(resetButton, &QPushButton::clicked, this, &DataMappingRulesDialog::onResetClicked);
    
    mappingButtonLayout->addWidget(addMappingButton);
    mappingButtonLayout->addWidget(editMappingButton);
    mappingButtonLayout->addWidget(deleteMappingButton);
    mappingButtonLayout->addStretch();
    mappingButtonLayout->addWidget(resetButton);
    
    mappingLayout->addLayout(mappingButtonLayout);
    mainLayout->addWidget(mappingGroup);
    
    // 底部按钮
    buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    
    okButton = new QPushButton("确定");
    okButton->setStyleSheet("QPushButton { background-color: #3498db; color: white; }");
    connect(okButton, &QPushButton::clicked, this, &QDialog::accept);
    
    cancelButton = new QPushButton("取消");
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
    
    buttonLayout->addWidget(okButton);
    buttonLayout->addWidget(cancelButton);
    
    mainLayout->addLayout(buttonLayout);
}

void DataMappingRulesDialog::loadConfigs()
{
    allConfigs = loadAllConfigs();
}

void DataMappingRulesDialog::updateConfigComboBox()
{
    configComboBox->clear();
    configComboBox->addItem("<新建配置>", "");
    
    for (const auto &config : allConfigs) {
        QString displayText = QString("%1 (%2)").arg(config.configName, config.deviceModel);
        configComboBox->addItem(displayText, config.configName);
    }
}

void DataMappingRulesDialog::updateMappingTable()
{
    mappingTable->setRowCount(currentConfig.mappings.size());
    
    for (int i = 0; i < currentConfig.mappings.size(); ++i) {
        const auto &mapping = currentConfig.mappings[i];
        
        QTableWidgetItem *columnItem = new QTableWidgetItem(mapping.excelColumn);
        columnItem->setTextAlignment(Qt::AlignCenter);
        mappingTable->setItem(i, 0, columnItem);
        
        QStringList rowNumbers;
        for (int row : mapping.csvRows) {
            rowNumbers.append(QString::number(row));
        }
        QString rowText = rowNumbers.join(", ");
        QTableWidgetItem *rowItem = new QTableWidgetItem(rowText);
        rowItem->setTextAlignment(Qt::AlignCenter);
        mappingTable->setItem(i, 1, rowItem);
        
        QTableWidgetItem *descItem = new QTableWidgetItem(mapping.description);
        mappingTable->setItem(i, 2, descItem);
    }
}

void DataMappingRulesDialog::updateButtonStates()
{
    bool hasSelection = mappingTable->currentRow() >= 0;
    bool hasConfig = !currentConfig.configName.isEmpty();
    
    editMappingButton->setEnabled(hasSelection);
    deleteMappingButton->setEnabled(hasSelection);
    saveConfigButton->setEnabled(hasConfig && configModified);
    deleteConfigButton->setEnabled(hasConfig);
}

void DataMappingRulesDialog::onConfigSelectionChanged()
{
    QString configName = configComboBox->currentData().toString();
    
    if (configName.isEmpty()) {
        // 新建配置
        currentConfig = DataMappingConfig();
        currentConfig.deviceModel = currentDeviceModel;
        currentConfig.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    } else {
        // 加载现有配置
        currentConfig = loadConfig(configName);
    }
    
    updateMappingTable();
    setConfigModified(false);
    updateButtonStates();
}

void DataMappingRulesDialog::onNewConfigClicked()
{
    configComboBox->setCurrentIndex(0); // 选择"<新建配置>"
}

void DataMappingRulesDialog::onSaveConfigClicked()
{
    if (!validateConfig()) {
        return;
    }
    
    if (saveCurrentConfig()) {
        QMessageBox::information(this, "成功", "配置保存成功！");
        loadConfigs();
        updateConfigComboBox();
        setConfigModified(false);
        updateButtonStates();
    }
}

void DataMappingRulesDialog::onSaveAsConfigClicked()
{
    bool ok;
    QString newName = QInputDialog::getText(this, "另存为", "请输入新配置名称:", 
                                          QLineEdit::Normal, currentConfig.configName, &ok);
    
    if (ok && !newName.trimmed().isEmpty()) {
        currentConfig.configName = newName.trimmed();
        currentConfig.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        
        if (saveCurrentConfig()) {
            QMessageBox::information(this, "成功", "配置另存为成功！");
            loadConfigs();
            updateConfigComboBox();
            
            // 选择新保存的配置
            for (int i = 0; i < configComboBox->count(); ++i) {
                if (configComboBox->itemData(i).toString() == newName) {
                    configComboBox->setCurrentIndex(i);
                    break;
                }
            }
            
            setConfigModified(false);
            updateButtonStates();
        }
    }
}

void DataMappingRulesDialog::onDeleteConfigClicked()
{
    if (currentConfig.configName.isEmpty()) {
        return;
    }
    
    int ret = QMessageBox::question(this, "确认删除", 
        QString("确定要删除配置 '%1' 吗？\n此操作不可撤销。").arg(currentConfig.configName),
        QMessageBox::Yes | QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        if (deleteConfig(currentConfig.configName)) {
            QMessageBox::information(this, "成功", "配置删除成功！");
            loadConfigs();
            updateConfigComboBox();
            configComboBox->setCurrentIndex(0); // 选择"<新建配置>"
        } else {
            QMessageBox::warning(this, "错误", "删除配置失败！");
        }
    }
}

void DataMappingRulesDialog::onAddMappingClicked()
{
    DataMappingRule newRule;
    MappingRuleEditDialog dialog(newRule, excelHeaders, csvRowNames, this);
    
    if (dialog.exec() == QDialog::Accepted) {
        DataMappingRule rule = dialog.getRule();
        
        // 检查是否已存在相同的Excel列
        for (const auto &existing : currentConfig.mappings) {
            if (existing.excelColumn == rule.excelColumn) {
                QMessageBox::warning(this, "重复映射", 
                    QString("Excel列 '%1' 已存在映射关系！").arg(rule.excelColumn));
                return;
            }
        }
        
        currentConfig.mappings.append(rule);
        updateMappingTable();
        setConfigModified(true);
        updateButtonStates();
    }
}

void DataMappingRulesDialog::onEditMappingClicked()
{
    int currentRow = mappingTable->currentRow();
    if (currentRow < 0 || currentRow >= currentConfig.mappings.size()) {
        return;
    }
    
    DataMappingRule rule = currentConfig.mappings[currentRow];
    MappingRuleEditDialog dialog(rule, excelHeaders, csvRowNames, this);
    
    if (dialog.exec() == QDialog::Accepted) {
        DataMappingRule newRule = dialog.getRule();
        
        // 检查是否与其他映射冲突（排除当前编辑的项）
        for (int i = 0; i < currentConfig.mappings.size(); ++i) {
            if (i != currentRow && currentConfig.mappings[i].excelColumn == newRule.excelColumn) {
                QMessageBox::warning(this, "重复映射", 
                    QString("Excel列 '%1' 已存在映射关系！").arg(newRule.excelColumn));
                return;
            }
        }
        
        currentConfig.mappings[currentRow] = newRule;
        updateMappingTable();
        setConfigModified(true);
        updateButtonStates();
    }
}

void DataMappingRulesDialog::onDeleteMappingClicked()
{
    int currentRow = mappingTable->currentRow();
    if (currentRow < 0 || currentRow >= currentConfig.mappings.size()) {
        return;
    }
    
    int ret = QMessageBox::question(this, "确认删除", 
        "确定要删除选中的映射关系吗？",
        QMessageBox::Yes | QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        currentConfig.mappings.removeAt(currentRow);
        updateMappingTable();
        setConfigModified(true);
        updateButtonStates();
    }
}

void DataMappingRulesDialog::onTableSelectionChanged()
{
    updateButtonStates();
}

void DataMappingRulesDialog::onResetClicked()
{
    int ret = QMessageBox::question(this, "确认重置", 
        "确定要清空所有映射关系吗？",
        QMessageBox::Yes | QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        currentConfig.mappings.clear();
        updateMappingTable();
        setConfigModified(true);
        updateButtonStates();
    }
}

QList<DataMappingRule> DataMappingRulesDialog::getMappingRules() const
{
    return currentConfig.mappings;
}

void DataMappingRulesDialog::setMappingRules(const QList<DataMappingRule> &rules)
{
    currentConfig.mappings = rules;
    updateMappingTable();
    setConfigModified(true);
    updateButtonStates();
}

DataMappingConfig DataMappingRulesDialog::getCurrentConfig() const
{
    return currentConfig;
}

bool DataMappingRulesDialog::saveCurrentConfig()
{
    if (currentConfig.configName.isEmpty()) {
        bool ok;
        QString name = QInputDialog::getText(this, "保存配置", "请输入配置名称:", 
                                           QLineEdit::Normal, "", &ok);
        if (!ok || name.trimmed().isEmpty()) {
            return false;
        }
        currentConfig.configName = name.trimmed();
        currentConfig.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    }
    
    return saveConfig(currentConfig);
}

bool DataMappingRulesDialog::validateConfig() const
{
    if (currentConfig.mappings.isEmpty()) {
        QMessageBox::warning(const_cast<DataMappingRulesDialog*>(this), "验证失败", 
                           "请至少添加一个映射关系！");
        return false;
    }
    
    return true;
}

void DataMappingRulesDialog::setConfigModified(bool modified)
{
    configModified = modified;
    QString title = "数据对应规则配置";
    if (modified && !currentConfig.configName.isEmpty()) {
        title += QString(" - %1 *").arg(currentConfig.configName);
    } else if (!currentConfig.configName.isEmpty()) {
        title += QString(" - %1").arg(currentConfig.configName);
    }
    setWindowTitle(title);
}

// 静态方法实现
QString DataMappingRulesDialog::getConfigDir()
{
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/DataMappingConfigs";
    QDir dir;
    if (!dir.exists(configDir)) {
        dir.mkpath(configDir);
    }
    return configDir;
}

QList<DataMappingConfig> DataMappingRulesDialog::loadAllConfigs()
{
    QList<DataMappingConfig> configs;
    QString configDir = getConfigDir();
    
    QDir dir(configDir);
    QStringList filters;
    filters << "*.json";
    QStringList files = dir.entryList(filters, QDir::Files);
    
    for (const QString &fileName : files) {
        QString filePath = dir.absoluteFilePath(fileName);
        QFile file(filePath);
        
        if (file.open(QIODevice::ReadOnly)) {
            QByteArray data = file.readAll();
            QJsonDocument doc = QJsonDocument::fromJson(data);
            
            if (!doc.isNull() && doc.isObject()) {
                QJsonObject obj = doc.object();
                DataMappingConfig config;
                
                config.configName = obj["configName"].toString();
                config.deviceModel = obj["deviceModel"].toString();
                config.excelFilePattern = obj["excelFilePattern"].toString();
                config.createTime = obj["createTime"].toString();
                
                QJsonArray mappingsArray = obj["mappings"].toArray();
                for (const QJsonValue &value : mappingsArray) {
                    QJsonObject mappingObj = value.toObject();
                    DataMappingRule rule;
                    rule.excelColumn = mappingObj["excelColumn"].toString();
                    rule.description = mappingObj["description"].toString();
                    
                    // 支持新的csvRows数组格式，同时兼容旧的csvRow格式
                    if (mappingObj.contains("csvRows")) {
                        QJsonArray csvRowsArray = mappingObj["csvRows"].toArray();
                        for (const QJsonValue &rowValue : csvRowsArray) {
                            rule.csvRows.append(rowValue.toInt());
                        }
                    } else if (mappingObj.contains("csvRow")) {
                        // 向后兼容旧格式
                        int csvRow = mappingObj["csvRow"].toInt();
                        if (csvRow > 0) {
                            rule.csvRows.append(csvRow);
                        }
                    }
                    
                    config.mappings.append(rule);
                }
                
                configs.append(config);
            }
        }
    }
    
    return configs;
}

DataMappingConfig DataMappingRulesDialog::loadConfig(const QString &configName)
{
    QList<DataMappingConfig> configs = loadAllConfigs();
    for (const auto &config : configs) {
        if (config.configName == configName) {
            return config;
        }
    }
    return DataMappingConfig();
}

bool DataMappingRulesDialog::saveConfig(const DataMappingConfig &config)
{
    QString configDir = getConfigDir();
    QString fileName = QString("%1.json").arg(config.configName);
    QString filePath = QDir(configDir).absoluteFilePath(fileName);
    
    QJsonObject obj;
    obj["configName"] = config.configName;
    obj["deviceModel"] = config.deviceModel;
    obj["excelFilePattern"] = config.excelFilePattern;
    obj["createTime"] = config.createTime;
    
    QJsonArray mappingsArray;
    for (const auto &rule : config.mappings) {
        QJsonObject mappingObj;
        mappingObj["excelColumn"] = rule.excelColumn;
        mappingObj["description"] = rule.description;
        
        // 保存csvRows数组
        QJsonArray csvRowsArray;
        for (int row : rule.csvRows) {
            csvRowsArray.append(row);
        }
        mappingObj["csvRows"] = csvRowsArray;
        
        mappingsArray.append(mappingObj);
    }
    obj["mappings"] = mappingsArray;
    
    QJsonDocument doc(obj);
    
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        return true;
    }
    
    return false;
}

bool DataMappingRulesDialog::deleteConfig(const QString &configName)
{
    QString configDir = getConfigDir();
    QString fileName = QString("%1.json").arg(configName);
    QString filePath = QDir(configDir).absoluteFilePath(fileName);
    
    return QFile::remove(filePath);
}

// MappingRuleEditDialog 实现
MappingRuleEditDialog::MappingRuleEditDialog(const DataMappingRule &inputRule, const QStringList &headers, const QStringList &rowNames, QWidget *parent)
    : QDialog(parent), rule(inputRule), excelHeaders(headers), csvRowNames(rowNames)
{
    setWindowTitle("编辑映射规则");
    setModal(true);
    resize(500, 250);
    
    setupUI();
    updateUI();
}

MappingRuleEditDialog::~MappingRuleEditDialog()
{
}

void MappingRuleEditDialog::setupUI()
{
    mainLayout = new QVBoxLayout(this);
    
    // 表单布局
    formLayout = new QFormLayout();
    
    // Excel列序号
    excelColumnLabel = new QLabel("Excel列序号:");
    excelColumnComboBox = new QComboBox();
    formLayout->addRow(excelColumnLabel, excelColumnComboBox);
    
    // CSV行号
    csvRowLabel = new QLabel("CSV行号(可多选):");
    csvRowListWidget = new QListWidget();
    csvRowListWidget->setSelectionMode(QAbstractItemView::MultiSelection);
    csvRowListWidget->setMaximumHeight(150);
    formLayout->addRow(csvRowLabel, csvRowListWidget);
    
    // 描述
    descriptionLabel = new QLabel("描述:");
    descriptionEdit = new QLineEdit();
    descriptionEdit->setPlaceholderText("可选：描述此映射关系的用途");
    formLayout->addRow(descriptionLabel, descriptionEdit);
    
    mainLayout->addLayout(formLayout);
    
    // 按钮
    buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    
    okButton = new QPushButton("确定");
    okButton->setStyleSheet("QPushButton { background-color: #3498db; color: white; }");
    connect(okButton, &QPushButton::clicked, this, &MappingRuleEditDialog::accept);
    
    cancelButton = new QPushButton("取消");
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
    
    buttonLayout->addWidget(okButton);
    buttonLayout->addWidget(cancelButton);
    
    mainLayout->addLayout(buttonLayout);
    
    // 连接信号，当下拉框选择改变时自动更新描述
    connect(excelColumnComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &MappingRuleEditDialog::onSelectionChanged);
    connect(csvRowListWidget, &QListWidget::itemSelectionChanged,
            this, &MappingRuleEditDialog::onSelectionChanged);
    
    populateComboBoxes();
}

void MappingRuleEditDialog::populateComboBoxes()
{
    // 填充Excel列序号，如果有完整的表头信息则显示完整信息
    if (!excelHeaders.isEmpty()) {
        for (int i = 0; i < excelHeaders.size(); ++i) {
            QString columnLetter = QString(QChar('A' + (i % 26)));
            if (i >= 26) {
                columnLetter = QString(QChar('A' + (i / 26 - 1))) + QString(QChar('A' + (i % 26)));
            }
            QString displayText = QString("%1 %2").arg(columnLetter).arg(excelHeaders[i]);
            excelColumnComboBox->addItem(displayText, columnLetter);
        }
    } else {
        // 如果没有表头信息，使用默认的A-Z, AA-AZ等
        for (int i = 0; i < 26; ++i) {
            QString col = QString(QChar('A' + i));
            excelColumnComboBox->addItem(col, col);
        }
        for (int i = 0; i < 26; ++i) {
            for (int j = 0; j < 26; ++j) {
                QString col = QString(QChar('A' + i)) + QString(QChar('A' + j));
                excelColumnComboBox->addItem(col, col);
            }
        }
    }
    
    // 填充CSV行号，如果有行名称信息则显示完整信息
    if (!csvRowNames.isEmpty()) {
        for (int i = 0; i < csvRowNames.size(); ++i) {
            QString displayText = QString("%1 %2").arg(i + 1).arg(csvRowNames[i]);
            QListWidgetItem *item = new QListWidgetItem(displayText);
            item->setData(Qt::UserRole, i + 1);
            csvRowListWidget->addItem(item);
        }
    } else {
        // 如果没有行名称信息，使用默认的1-100
        for (int i = 1; i <= 100; ++i) {
            QListWidgetItem *item = new QListWidgetItem(QString::number(i));
            item->setData(Qt::UserRole, i);
            csvRowListWidget->addItem(item);
        }
    }
}

void MappingRuleEditDialog::updateUI()
{
    // 设置当前值
    // 查找Excel列序号对应的索引
    int excelIndex = -1;
    for (int i = 0; i < excelColumnComboBox->count(); ++i) {
        if (excelColumnComboBox->itemData(i).toString() == rule.excelColumn) {
            excelIndex = i;
            break;
        }
    }
    if (excelIndex >= 0) {
        excelColumnComboBox->setCurrentIndex(excelIndex);
    }
    
    // 设置CSV行号的选择状态
    for (int i = 0; i < csvRowListWidget->count(); ++i) {
        QListWidgetItem *item = csvRowListWidget->item(i);
        int rowNumber = item->data(Qt::UserRole).toInt();
        if (rule.csvRows.contains(rowNumber)) {
            item->setSelected(true);
        }
    }
    
    descriptionEdit->setText(rule.description);
}

void MappingRuleEditDialog::accept()
{
    // 从ComboBox的userData中获取实际值
    rule.excelColumn = excelColumnComboBox->currentData().toString();
    
    // 获取选中的CSV行号
    rule.csvRows.clear();
    QList<QListWidgetItem*> selectedItems = csvRowListWidget->selectedItems();
    for (QListWidgetItem *item : selectedItems) {
        int rowNumber = item->data(Qt::UserRole).toInt();
        if (rowNumber > 0) {
            rule.csvRows.append(rowNumber);
        }
    }
    
    rule.description = descriptionEdit->text().trimmed();
    
    if (rule.excelColumn.isEmpty() || rule.csvRows.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请选择有效的Excel列序号和至少一个CSV行号！");
        return;
    }
    
    QDialog::accept();
}

DataMappingRule MappingRuleEditDialog::getRule() const
{
    return rule;
}

void MappingRuleEditDialog::onSelectionChanged()
{
    // 当Excel列序号和CSV行号都有选择时，自动生成描述
    QList<QListWidgetItem*> selectedItems = csvRowListWidget->selectedItems();
    if (excelColumnComboBox->currentIndex() >= 0 && !selectedItems.isEmpty()) {
        QString excelText = excelColumnComboBox->currentText();
        
        QStringList csvTexts;
        for (QListWidgetItem *item : selectedItems) {
            csvTexts.append(item->text());
        }
        
        // 生成描述："A - 姓名"→"19 - 皮皮, 20 - 小明"
        QString csvText = csvTexts.join(", ");
        QString description = QString("\"%1\"→\"%2\"").arg(excelText).arg(csvText);
        descriptionEdit->setText(description);
    }
}