#ifndef PARSEITEMDIALOG_H
#define PARSEITEMDIALOG_H

#include <QDialog>
#include <QComboBox>
#include <QLineEdit>
#include <QSpinBox>
#include <QLabel>
#include <QPushButton>
#include <QGridLayout>
#include <QDialogButtonBox>
#include <QCheckBox>
#include <QList>

// 包含ParseItem结构体定义
#include "setvaluedialog.h"

class ParseItemDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ParseItemDialog(ParseItem &item, QWidget *parent = nullptr);
    ~ParseItemDialog();

private slots:
    void onDataTypeChanged(const QString &type);
    void onParseMethodChanged(const QString &method);
    void accept() override;

private:
    ParseItem &parseItem;
    
    // UI组件
    QLineEdit *nameEdit;
    QLineEdit *descriptionEdit;
    QComboBox *dataTypeCombo;
    QComboBox *readDirectionCombo;
    QSpinBox *samplePeriodSpin;
    QSpinBox *deviceTypeIdSpin;
    QSpinBox *deviceAddressSpin;
    QComboBox *parseMethodCombo;
    QSpinBox *bitOffsetSpin;
    QCheckBox *bcdParseCheck;
    
    // 新增字段的UI组件
    QLineEdit *addressEdit;
    QComboBox *processMethodCombo;
    QLineEdit *minValueEdit;
    QLineEdit *maxValueEdit;
    QLineEdit *unitEdit;
    QLineEdit *processParamEdit;
    // QCheckBox *enableProtectionCheck; // 已移除启用保护功能复选框
    QLineEdit *expectedDataEdit;
    
    // 批量增加相关UI组件
    QComboBox *batchCountCombo;
    QPushButton *batchAddButton;
    
    // 初始化UI
    void initUI();
    void updateUIState();
    
public slots:
    void onBatchAddClicked();
    
signals:
    void batchItemsCreated(const QList<ParseItem> &items);
};

#endif // PARSEITEMDIALOG_H