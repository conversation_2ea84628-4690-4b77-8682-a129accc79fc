#include "parseitemdialog.h"
#include "setvaluedialog.h" // 包含ParseItem结构体定义
#include <QVBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QCheckBox>
#include <QMessageBox>

ParseItemDialog::ParseItemDialog(ParseItem &item, QWidget *parent)
    : QDialog(parent), parseItem(item)
{
    setWindowTitle(tr("编辑数据标签"));
    setMinimumWidth(400);
    
    initUI();
    
    // 设置初始值
    nameEdit->setText(item.name);
    descriptionEdit->setText(item.description);
    dataTypeCombo->setCurrentText(item.dataType);
    readDirectionCombo->setCurrentText(item.readDirection);
    samplePeriodSpin->setValue(item.samplePeriod);
    deviceTypeIdSpin->setValue(item.deviceTypeId);
    deviceAddressSpin->setValue(item.deviceAddress);
    parseMethodCombo->setCurrentText(item.parseMethod);
    bitOffsetSpin->setValue(item.bitOffset);
    bcdParseCheck->setChecked(item.bcdParse);
    
    // 设置新增字段的初始值
    addressEdit->setText(item.address);
    processMethodCombo->setCurrentText(item.processMethod);
    minValueEdit->setText(QString::number(item.minValue));
    maxValueEdit->setText(QString::number(item.maxValue));
    unitEdit->setText(item.unit);
    processParamEdit->setText(QString::number(item.processParam));
    // enableProtectionCheck->setChecked(item.enableProtection); // 已移除启用保护功能复选框
    expectedDataEdit->setText(item.expectedData);
    
    // 根据当前数据类型和解析方式更新UI状态
    updateUIState();
}

ParseItemDialog::~ParseItemDialog()
{
}

void ParseItemDialog::initUI()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // 创建表单布局
    QFormLayout *formLayout = new QFormLayout();
    
    // 创建UI组件
    nameEdit = new QLineEdit(this);
    descriptionEdit = new QLineEdit(this);
    
    // 数据类型下拉框
    dataTypeCombo = new QComboBox(this);
    dataTypeCombo->addItems(QStringList() << "UINT8" << "INT8" << "UINT16" << "INT16" 
                          << "UINT32" << "INT32" << "FLOAT" << "DOUBLE" 
                          << "BIT" << "STRING" << "BCD" << "CUSTOM_SCALE");
    connect(dataTypeCombo, &QComboBox::currentTextChanged, this, &ParseItemDialog::onDataTypeChanged);
    
    // 读取方向下拉框
    readDirectionCombo = new QComboBox(this);
    readDirectionCombo->addItems(QStringList() << "只读" << "读写");
    
    // 采集周期
    samplePeriodSpin = new QSpinBox(this);
    samplePeriodSpin->setRange(0, 60000);
    samplePeriodSpin->setSingleStep(100);
    samplePeriodSpin->setSuffix(" ms");
    
    // 存储器类型序号
    deviceTypeIdSpin = new QSpinBox(this);
    deviceTypeIdSpin->setRange(0, 255);
    
    // 存储器地址
    deviceAddressSpin = new QSpinBox(this);
    deviceAddressSpin->setRange(0, 65535);
    
    // 解析方式下拉框
    parseMethodCombo = new QComboBox(this);
    parseMethodCombo->addItems(QStringList() 
        << "按位取" 
        << "2字节 无符号 先高后低" 
        << "2字节 无符号 先低后高" 
        << "2字节 有符号 先高后低" 
        << "2字节 有符号 先低后高" 
        << "2字节 无符号 取高字节" 
        << "2字节 无符号 取低字节" 
        << "2字节 有符号 取高字节" 
        << "2字节 有符号 取低字节" 
        << "4字节 无符号 顺序4321" 
        << "4字节 无符号 顺序2143" 
        << "4字节 无符号 顺序1234" 
        << "4字节 无符号 顺序3412" 
        << "4字节 有符号 顺序4321" 
        << "4字节 有符号 顺序2143" 
        << "4字节 有符号 顺序1234" 
        << "4字节 有符号 顺序3412");
    connect(parseMethodCombo, &QComboBox::currentTextChanged, this, &ParseItemDialog::onParseMethodChanged);
    
    // 位偏移量
    bitOffsetSpin = new QSpinBox(this);
    bitOffsetSpin->setRange(0, 31);
    
    // BCD解析复选框
    bcdParseCheck = new QCheckBox(tr("按BCD码解析"), this);
    
    // 创建复选框水平布局
    QHBoxLayout *checkBoxLayout = new QHBoxLayout();
    checkBoxLayout->addWidget(bcdParseCheck);
    checkBoxLayout->addStretch(); // 添加弹性空间
    
    // 创建新增字段的UI组件
    addressEdit = new QLineEdit(this);
    
    processMethodCombo = new QComboBox(this);
    processMethodCombo->addItems(QStringList() << "原始数据" << "除以处理参数" << "乘以处理参数" << "自定义比例" << "D1(智能除法)" << "DM1(智能除法)" << "DM2(智能除法)");
    
    minValueEdit = new QLineEdit(this);
    minValueEdit->setValidator(new QDoubleValidator(this));
    
    maxValueEdit = new QLineEdit(this);
    maxValueEdit->setValidator(new QDoubleValidator(this));
    
    unitEdit = new QLineEdit(this);
    
    processParamEdit = new QLineEdit(this);
    processParamEdit->setValidator(new QDoubleValidator(0.001, 10000, 3, this));
    processParamEdit->setPlaceholderText("1.0（不处理）");
    
    expectedDataEdit = new QLineEdit(this);
    
    // 创建批量增加相关组件
    batchCountCombo = new QComboBox(this);
    batchCountCombo->addItems(QStringList() << "1" << "5" << "10" << "20" << "50");
    batchCountCombo->setCurrentText("1");
    
    batchAddButton = new QPushButton(tr("批量增加"), this);
    connect(batchAddButton, &QPushButton::clicked, this, &ParseItemDialog::onBatchAddClicked);
    
    // 创建批量增加的水平布局
    QHBoxLayout *batchLayout = new QHBoxLayout();
    batchLayout->addWidget(new QLabel(tr("批量增加数量:"), this));
    batchLayout->addWidget(batchCountCombo);
    batchLayout->addWidget(batchAddButton);
    batchLayout->addStretch();
    
    // 添加到表单布局
    formLayout->addRow(tr("名称(英文):"), nameEdit);
    formLayout->addRow(tr("描述(中文):"), descriptionEdit);
    formLayout->addRow(tr("数据类型:"), dataTypeCombo);
    formLayout->addRow(tr("读取方向:"), readDirectionCombo);
    formLayout->addRow(tr("采集周期:"), samplePeriodSpin);
    formLayout->addRow(tr("存储器类型序号:"), deviceTypeIdSpin);
    formLayout->addRow(tr("存储器地址:"), deviceAddressSpin);
    formLayout->addRow(tr("解析方式:"), parseMethodCombo);
    formLayout->addRow(tr("位偏移量:"), bitOffsetSpin);
    formLayout->addRow("", checkBoxLayout);
    
    // 添加新增字段到表单布局
    formLayout->addRow(tr("地址:"), addressEdit);
    formLayout->addRow(tr("处理方式:"), processMethodCombo);
    formLayout->addRow(tr("最小值:"), minValueEdit);
    formLayout->addRow(tr("最大值:"), maxValueEdit);
    formLayout->addRow(tr("单位:"), unitEdit);
    formLayout->addRow(tr("数据处理参数:"), processParamEdit);
    formLayout->addRow(tr("期望数据:"), expectedDataEdit);
    
    // 添加批量增加布局
    formLayout->addRow("", batchLayout);
    
    // 创建按钮
    QDialogButtonBox *buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, this);
    connect(buttonBox, &QDialogButtonBox::accepted, this, &ParseItemDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, this, &ParseItemDialog::reject);
    
    // 添加到主布局
    mainLayout->addLayout(formLayout);
    mainLayout->addWidget(buttonBox);
    
    setLayout(mainLayout);
}

void ParseItemDialog::updateUIState()
{
    // 根据数据类型更新UI状态
    QString dataType = dataTypeCombo->currentText();
    QString parseMethod = parseMethodCombo->currentText();
    
    // 位偏移量只在按位取或BIT类型时可用
    bool isBitMode = (parseMethod == "按位取" || dataType == "BIT");
    bitOffsetSpin->setEnabled(isBitMode);
    
    // BCD解析只在特定类型时可用
    bool isBcdAvailable = (dataType == "UINT16" || dataType == "UINT32" || dataType == "BCD");
    bcdParseCheck->setEnabled(isBcdAvailable);
    
    // 如果是BIT类型，强制使用按位取解析方式
    if (dataType == "BIT" && parseMethod != "按位取") {
        parseMethodCombo->setCurrentText("按位取");
    }
    
    // 如果是STRING类型，禁用BCD解析
    if (dataType == "STRING") {
        bcdParseCheck->setChecked(false);
    }
}

void ParseItemDialog::onDataTypeChanged(const QString &type)
{
    // 根据数据类型更新UI状态
    updateUIState();
}

void ParseItemDialog::onParseMethodChanged(const QString &method)
{
    // 根据解析方式更新UI状态
    updateUIState();
}

void ParseItemDialog::accept()
{
    // 验证输入
    if (nameEdit->text().isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("名称不能为空"));
        nameEdit->setFocus();
        return;
    }
    
    if (descriptionEdit->text().isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("描述不能为空"));
        descriptionEdit->setFocus();
        return;
    }
    
    // 更新解析项数据
    parseItem.name = nameEdit->text();
    parseItem.description = descriptionEdit->text();
    parseItem.dataType = dataTypeCombo->currentText();
    parseItem.readDirection = readDirectionCombo->currentText();
    parseItem.samplePeriod = samplePeriodSpin->value();
    parseItem.deviceTypeId = deviceTypeIdSpin->value();
    parseItem.deviceAddress = deviceAddressSpin->value();
    parseItem.parseMethod = parseMethodCombo->currentText();
    parseItem.bitOffset = bitOffsetSpin->value();
    parseItem.bcdParse = bcdParseCheck->isChecked() ? 1 : 0;
    
    // 更新新增字段的数据
    parseItem.address = addressEdit->text();
    parseItem.processMethod = processMethodCombo->currentText();
    parseItem.minValue = minValueEdit->text().toDouble();
    parseItem.maxValue = maxValueEdit->text().toDouble();
    parseItem.unit = unitEdit->text();
    parseItem.processParam = processParamEdit->text().isEmpty() ? 1.0 : processParamEdit->text().toDouble();
    // parseItem.enableProtection = enableProtectionCheck->isChecked(); // 已移除启用保护功能复选框
    parseItem.enableProtection = false; // 默认关闭保护功能
    parseItem.expectedData = expectedDataEdit->text();
    // 注意：protectionStatus、actualData和compareResult不在编辑对话框中设置，它们由系统自动填入
    
    // 调用基类的accept方法关闭对话框
    QDialog::accept();
}

void ParseItemDialog::onBatchAddClicked()
{
    // 获取批量增加的数量
    int batchCount = batchCountCombo->currentText().toInt();
    
    // 获取当前表单中的基础数据作为模板
    ParseItem templateItem;
    templateItem.name = nameEdit->text().isEmpty() ? "Tag" : nameEdit->text();
    templateItem.description = descriptionEdit->text().isEmpty() ? "标签" : descriptionEdit->text();
    templateItem.dataType = dataTypeCombo->currentText();
    templateItem.readDirection = readDirectionCombo->currentText();
    templateItem.samplePeriod = samplePeriodSpin->value();
    templateItem.deviceTypeId = deviceTypeIdSpin->value();
    templateItem.deviceAddress = deviceAddressSpin->value();
    templateItem.parseMethod = parseMethodCombo->currentText();
    templateItem.bitOffset = bitOffsetSpin->value();
    templateItem.bcdParse = bcdParseCheck->isChecked() ? 1 : 0;
    templateItem.processMethod = processMethodCombo->currentText();
    templateItem.minValue = minValueEdit->text().isEmpty() ? 0.0 : minValueEdit->text().toDouble();
    templateItem.maxValue = maxValueEdit->text().isEmpty() ? 100.0 : maxValueEdit->text().toDouble();
    templateItem.unit = unitEdit->text();
    templateItem.processParam = processParamEdit->text().isEmpty() ? 1.0 : processParamEdit->text().toDouble();
    // templateItem.enableProtection = enableProtectionCheck->isChecked(); // 已移除启用保护功能复选框
    templateItem.enableProtection = false; // 默认关闭保护功能
    templateItem.expectedData = expectedDataEdit->text();
    
    // 获取基础地址
    int baseAddress = 40000;
    if (!addressEdit->text().isEmpty()) {
        bool ok;
        int addr = addressEdit->text().toInt(&ok);
        if (ok) {
            baseAddress = addr;
        }
    }
    
    // 创建批量项目列表
    QList<ParseItem> batchItems;
    for (int i = 0; i < batchCount; i++) {
        ParseItem newItem = templateItem;
        
        // 自动生成名称和描述（如果原来为空或使用默认值）
        if (templateItem.name == "Tag" || templateItem.name.isEmpty()) {
            newItem.name = "Tag" + QString::number(i + 1);
        } else {
            newItem.name = templateItem.name + QString::number(i + 1);
        }
        
        if (templateItem.description == "标签" || templateItem.description.isEmpty()) {
            newItem.description = "标签" + QString::number(i + 1);
        } else {
            newItem.description = templateItem.description + QString::number(i + 1);
        }
        
        // 地址递增
        newItem.address = QString::number(baseAddress + i);
        
        // 初始化其他字段
        newItem.protectionStatus = "";
        newItem.actualData = "";
        newItem.compareResult = "";
        
        batchItems.append(newItem);
    }
    
    // 发射信号，通知父窗口创建了批量项目
    emit batchItemsCreated(batchItems);
    
    // 关闭对话框
    accept();
}